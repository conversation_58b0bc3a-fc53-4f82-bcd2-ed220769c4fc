﻿@page "/ecn/upload"
@rendermode InteractiveServer
@inject ECNUploadService UploadService
@inject ECNImageProcessingService ImageProcessingService
@inject IJSRuntime JSRuntime
@using ProjectECN.Services
@implements IAsyncDisposable

<div class="ecn-upload-container">
    <div class="upload-header">
        <h2>📁 Engineering Change Notice Upload</h2>
        <p class="upload-description">Upload daily ECN files in YYYYMMDD.zip format</p>
    </div>

    <div class="upload-card">
        @if (!_isUploading)
        {
            <div class="file-input-section">
                <h3>Select ECN File</h3>
                <p class="file-format-info">Accepted format: YYYYMMDD.zip (e.g., 20250702.zip)</p>

                <InputFile @ref="_fileInput"
                           accept=".zip"
                           OnChange="HandleFileSelected"
                           class="file-input"
                           multiple="false"/>

                <button type="button"
                        class="browse-button"
                        @onclick="OpenFileDialog">
                    📂 Browse Files
                </button>
            </div>

            @if (_selectedFile != null)
            {
                <div class="selected-file-info">
                    <div class="file-icon">📄</div>
                    <div class="file-details">
                        <strong>@_selectedFile.Name</strong>
                        <span class="file-size">@FormatFileSize(_selectedFile.Size)</span>
                    </div>
                    <button type="button"
                            class="upload-button"
                            @onclick="StartUpload">
                        ⬆️ Upload & Extract
                    </button>
                </div>
            }
        }
        else
        {
            <div class="upload-progress-container">
                <div class="progress-header">
                    <h3>🔄 Processing ECN File</h3>
                </div>

                <div class="progress-details">
                    <div class="current-stage">
                        <strong>Current Stage:</strong> @_currentProgress.Stage
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: @(_currentProgress.Percentage)%"></div>
                        </div>
                        <div class="progress-percentage">
                            @_currentProgress.Percentage%
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(_selectedFile?.Name))
                {
                    <div class="processing-file">
                        Processing: <strong>@_selectedFile.Name</strong>
                    </div>
                }

                <div class="upload-actions">
                    <button type="button"
                            class="cancel-button"
                            @onclick="CancelUpload">
                        ❌ Cancel Upload
                    </button>
                </div>
            </div>
        }

        @if (!string.IsNullOrEmpty(_resultMessage))
        {
            <div class="message-bar @(_isSuccess ? "success" : "error")">
                @_resultMessage
            </div>
        }
    </div>

    <div class="upload-instructions">
        <h4>Upload Instructions:</h4>
        <ul>
            <li>File must be in ZIP format with name pattern YYYYMMDD.zip</li>
            <li>Re-uploading the same date will delete existing data and re-process everything</li>
            <li>Maximum file size: 100MB</li>
            <li>Files will be extracted to the ECN folder automatically</li>
            <li>TIFF images will be processed to extract ECN Description and Factory boxes</li>
            <li>Excel files will be processed and ECN data saved to database</li>
            <li>Factory images will be analyzed with OCR to detect PSMC ECNs</li>
            <li>Extracted image portions will be saved as PNG files in the same folders</li>
            <li>Original ZIP file will be deleted after successful processing</li>
        </ul>
    </div>
</div>

@code {
    private InputFile? _fileInput;
    private IBrowserFile? _selectedFile;
    private bool _isUploading;
    private string _resultMessage = string.Empty;
    private bool _isSuccess;
    private UploadProgress _currentProgress = new();
    private CancellationTokenSource? _uploadCancellationTokenSource;
    private bool _isDisposed;

    private Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        try
        {
            Console.WriteLine($"File selected: {e.File?.Name}, Size: {e.File?.Size}");

            // Clear any previous results and cancel ongoing operations
            _resultMessage = string.Empty;
            _uploadCancellationTokenSource?.Cancel();
            _uploadCancellationTokenSource?.Dispose();
            _uploadCancellationTokenSource = null;

            // Reset upload state
            _isUploading = false;

            // Set the new file
            _selectedFile = e.File;

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in HandleFileSelected: {ex}");
            _resultMessage = $"Error selecting file: {ex.Message}";
            _isSuccess = false;
            _selectedFile = null;
            StateHasChanged();
        }

        return Task.CompletedTask;
    }

    private async Task OpenFileDialog()
    {
        if (_isDisposed)
            return;

        try
        {
            Console.WriteLine("Opening file dialog...");

            // Reset the file input to clear any previous selection
            await ResetFileInput();

            await JSRuntime.InvokeVoidAsync("eval", "document.querySelector('.file-input').click()");
        }
        catch (JSDisconnectedException)
        {
            Console.WriteLine("Circuit disconnected during file dialog open");
            // Circuit has been disconnected, ignore the error
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error opening file dialog: {ex}");
            _resultMessage = $"Error opening file dialog: {ex.Message}";
            _isSuccess = false;
            StateHasChanged();
        }
    }

    private async Task ResetFileInput()
    {
        try
        {
            // Reset the file input value to clear any cached file references
            await JSRuntime.InvokeVoidAsync("eval", "document.querySelector('.file-input').value = ''");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error resetting file input: {ex}");
            // Non-critical error, continue
        }
    }

    private async Task ClearSelectedFile()
    {
        try
        {
            _selectedFile = null;
            await ResetFileInput();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error clearing selected file: {ex}");
            _selectedFile = null;
            StateHasChanged();
        }
    }

    private async Task StartUpload()
    {
        if (_selectedFile == null || _isDisposed)
        {
            _resultMessage = "No file selected.";
            _isSuccess = false;
            StateHasChanged();
            return;
        }

        // Validate file before processing
        try
        {
            // Test file access to ensure it's still valid
            var testSize = _selectedFile.Size;
            var testName = _selectedFile.Name;

            if (testSize <= 0)
            {
                _resultMessage = "Invalid file selected.";
                _isSuccess = false;
                StateHasChanged();
                return;
            }

            Console.WriteLine($"File validation passed: {testName}, Size: {testSize}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"File validation failed: {ex}");
            _resultMessage = $"File is no longer accessible. Please select the file again. Error: {ex.Message}";
            _isSuccess = false;
            _selectedFile = null; // Clear the invalid file reference
            StateHasChanged();
            return;
        }

        // Cancel any existing upload
        _uploadCancellationTokenSource?.Cancel();
        _uploadCancellationTokenSource = new CancellationTokenSource();

        _isUploading = true;
        _resultMessage = string.Empty;
        _currentProgress = new UploadProgress { Stage = "Starting", Percentage = 0 };
        StateHasChanged();

        var progress = new Progress<UploadProgress>(p =>
        {
            if (!_isDisposed)
            {
                _currentProgress = p;
                InvokeAsync(StateHasChanged);
            }
        });

        try
        {
            Console.WriteLine($"Starting upload for file: {_selectedFile.Name}, Size: {_selectedFile.Size}");

            // Create a local reference to avoid potential issues with the file reference
            var fileToUpload = _selectedFile;

            var result = await UploadService.UploadAndExtractECNFileAsync(fileToUpload, progress, _uploadCancellationTokenSource.Token);
            Console.WriteLine($"Upload result: Success={result.Success}, Message={result.Message}, Error={result.ErrorMessage}");

            if (!_isDisposed)
            {
                _isSuccess = result.Success;
                _resultMessage = result.Success ? result.Message : result.ErrorMessage;

                if (result.Success)
                {
                    await ClearSelectedFile();
                }
            }
        }
        catch (JSDisconnectedException)
        {
            Console.WriteLine("Circuit disconnected during upload");
            if (!_isDisposed)
            {
                _isSuccess = false;
                _resultMessage = "Connection lost during upload. Please try again.";
            }
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Upload was cancelled");
            if (!_isDisposed)
            {
                _isSuccess = false;
                _resultMessage = "Upload was cancelled.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Upload failed with exception: {ex}");
            if (!_isDisposed)
            {
                _isSuccess = false;
                _resultMessage = $"Upload failed: {ex.Message}";

                // If the error suggests file reference issues, clear the selected file
                if (ex.Message.Contains("_blazorFilesById") ||
                    ex.Message.Contains("file stream") ||
                    ex.Message.Contains("file reference"))
                {
                    await ClearSelectedFile();
                    _resultMessage += " Please select the file again.";
                }
            }
        }
        finally
        {
            if (!_isDisposed)
            {
                _isUploading = false;
                StateHasChanged();
            }
        }
    }

    private void CancelUpload()
    {
        _uploadCancellationTokenSource?.Cancel();
        _isUploading = false;
        _resultMessage = "Upload cancelled by user.";
        _isSuccess = false;
        StateHasChanged();
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB"];
        double len = bytes;
        var order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }

        return $"{len:0.##} {sizes[order]}";
    }

    public async ValueTask DisposeAsync()
    {
        _isDisposed = true;

        // Cancel any ongoing upload
        _uploadCancellationTokenSource?.Cancel();
        _uploadCancellationTokenSource?.Dispose();

        // Clear references
        _selectedFile = null;
        _fileInput = null;
    }

}