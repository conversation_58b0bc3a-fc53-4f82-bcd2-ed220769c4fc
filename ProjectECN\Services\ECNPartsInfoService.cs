using Microsoft.EntityFrameworkCore;
using ProjectECN.DTO;
using ProjectECN.Models;

namespace ProjectECN.Services
{
    /// <summary>
    /// Service for retrieving ECN parts information and matching with KDBOM data
    /// </summary>
    public class ECNPartsInfoService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly ILogger<ECNPartsInfoService> _logger;

        public ECNPartsInfoService(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            ILogger<ECNPartsInfoService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        /// <summary>
        /// Get parts information for a specific ECN number
        /// </summary>
        /// <param name="ecnNumber">ECN number to search for</param>
        /// <returns>ECN parts information with KDBOM matching</returns>
        public async Task<ECNPartsInfoDto> GetECNPartsInfoAsync(string ecnNumber)
        {
            try
            {
                _logger.LogInformation($"Getting parts information for ECN: {ecnNumber}");

                await using var context = await _contextFactory.CreateDbContextAsync();

                // Get ECN parts data
                var ecnParts = await context.RawEcndata
                    .Where(r => r.EcnNo == ecnNumber)
                    .OrderBy(r => r.Page)
                    .ThenBy(r => r.Line)
                    .ToListAsync();

                if (!ecnParts.Any())
                {
                    _logger.LogWarning($"No parts found for ECN: {ecnNumber}");
                    return new ECNPartsInfoDto
                    {
                        EcnNumber = ecnNumber,
                        TotalParts = 0,
                        PartsFoundInKDBOM = 0
                    };
                }

                var result = new ECNPartsInfoDto
                {
                    EcnNumber = ecnNumber,
                    CreatedDate = ecnParts.FirstOrDefault()?.CreatedDate,
                    TotalParts = ecnParts.Count
                };

                // Process each part and check against KDBOM
                var partDetails = new List<ECNPartDetailDto>();
                int partsFoundInKDBOM = 0;

                foreach (var ecnPart in ecnParts)
                {
                    var partDetail = new ECNPartDetailDto
                    {
                        PartNumber = GetPrimaryPartNumber(ecnPart),
                        OldPartNumber = ecnPart.OldPartNumber ?? string.Empty,
                        NewPartNumber = ecnPart.NewPartNumber ?? string.Empty,
                        PartName = ecnPart.PartName ?? string.Empty,
                        BomGname = ecnPart.BomGname ?? string.Empty,
                        Page = ecnPart.Page ?? string.Empty,
                        Line = ecnPart.Line ?? string.Empty,
                        Block = ecnPart.Block ?? string.Empty,
                        DwgNumber = ecnPart.DwgNumber ?? string.Empty
                    };

                    // Check if part exists in KDBOM
                    var kdbomMatch = await FindPartInKDBOMAsync(context, partDetail.PartNumber, ecnPart.OldPartNumber, ecnPart.NewPartNumber);
                    
                    if (kdbomMatch != null)
                    {
                        partDetail.FoundInKDBOM = true;
                        partDetail.KDBOMMatch = kdbomMatch;
                        partsFoundInKDBOM++;
                    }

                    partDetails.Add(partDetail);
                }

                result.Parts = partDetails;
                result.PartsFoundInKDBOM = partsFoundInKDBOM;

                _logger.LogInformation($"Found {result.TotalParts} parts for ECN {ecnNumber}, {result.PartsFoundInKDBOM} found in KDBOM");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting parts information for ECN: {ecnNumber}");
                throw;
            }
        }

        /// <summary>
        /// Find a part in KDBOM data by part numbers
        /// </summary>
        private async Task<KDBOMMatchInfo?> FindPartInKDBOMAsync(ApplicationDbContext context, string primaryPartNumber, string? oldPartNumber, string? newPartNumber)
        {
            try
            {
                // Create list of part numbers to search for
                var partNumbersToSearch = new List<string> { primaryPartNumber };
                
                if (!string.IsNullOrWhiteSpace(oldPartNumber))
                    partNumbersToSearch.Add(oldPartNumber);
                
                if (!string.IsNullOrWhiteSpace(newPartNumber))
                    partNumbersToSearch.Add(newPartNumber);

                // Remove duplicates and empty values
                partNumbersToSearch = partNumbersToSearch
                    .Where(p => !string.IsNullOrWhiteSpace(p))
                    .Distinct()
                    .ToList();

                // Search in KDBOM PartsData
                var kdbomPart = await (from pd in context.KDBOMPartsData
                                     join fu in context.KDBOMFileUploads on pd.FileUploadId equals fu.Id
                                     where fu.ProcessingStatus == "Completed" &&
                                           partNumbersToSearch.Any(pn => 
                                               (pd.PartNo != null && pd.PartNo.Contains(pn)) ||
                                               (pd.MaterialPartNo != null && pd.MaterialPartNo.Contains(pn)) ||
                                               (pd.PBOMPartNumberParent != null && pd.PBOMPartNumberParent.Contains(pn)))
                                     select new { pd, fu })
                                     .FirstOrDefaultAsync();

                if (kdbomPart == null)
                    return null;

                // Get model information if available
                var modelInfo = await context.KDBOMModelInfos
                    .Where(mi => mi.FileUploadId == kdbomPart.fu.Id)
                    .FirstOrDefaultAsync();

                return new KDBOMMatchInfo
                {
                    ModelCode = kdbomPart.pd.KDModelCode ?? string.Empty,
                    ModelName = modelInfo?.ModelName ?? string.Empty,
                    SendType = kdbomPart.pd.SendType ?? string.Empty,
                    BOMGName = kdbomPart.pd.BOMGName ?? string.Empty,
                    Dealer = kdbomPart.pd.Dealer ?? string.Empty,
                    QTY = kdbomPart.pd.QTY ?? string.Empty,
                    InstallLocationComment = kdbomPart.pd.InstallLocationComment ?? string.Empty,
                    SheetName = kdbomPart.pd.SheetName ?? string.Empty,
                    FileName = kdbomPart.fu.FileName ?? string.Empty
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error searching for part in KDBOM: {primaryPartNumber}");
                return null;
            }
        }

        /// <summary>
        /// Get the primary part number to use for searching
        /// Prioritizes NewPartNumber, then OldPartNumber, then falls back to a combination
        /// </summary>
        private static string GetPrimaryPartNumber(Models.RawEcndatum ecnPart)
        {
            if (!string.IsNullOrWhiteSpace(ecnPart.NewPartNumber))
                return ecnPart.NewPartNumber;
            
            if (!string.IsNullOrWhiteSpace(ecnPart.OldPartNumber))
                return ecnPart.OldPartNumber;
            
            // Fallback to any available part identifier
            return ecnPart.PartName ?? ecnPart.DwgNumber ?? "Unknown";
        }
    }
}
