@page "/kdbom/import"
@rendermode InteractiveServer
@inject KDBOMService KDBOMService
@inject IJSRuntime JSRuntime
@inject IWebHostEnvironment WebHostEnvironment
@using ProjectECN.DTO.KDBOM
@using ProjectECN.Services
@implements IAsyncDisposable

<div class="kdbom-import-container">
    <div class="page-header">
        <h2>📊 KDBOM Import</h2>
        <p class="page-description">Import KDBOM Excel files with multiple sheets. Use the data management section to clear existing data if needed, then import your file.</p>
    </div>

    <!-- File Import Section -->
    <div class="import-card">
        <div class="section-header">
            <h3>📁 File Import</h3>
            <p class="section-description">Import KDBOM Excel files (data will be appended to existing records)</p>
        </div>

        @if (!_isProcessing && !_isClearingData)
        {
            <div class="file-selection-section">
                <p class="file-format-info">Select one KDBOM Excel file (.xlsx, .xls) to import (max 200MB)</p>

                <div class="file-input-wrapper">
                    <label class="file-input-label">
                        <InputFile accept=".xlsx,.xls"
                                   OnChange="HandleFileSelected"
                                   class="file-input-hidden"/>
                        <span class="browse-button">
                            📁 Browse File
                        </span>
                    </label>
                </div>

                @if (_selectedFile != null)
                {
                    <div class="selected-file-section">
                        <h4>Selected File</h4>
                        <div class="file-item">
                            <div class="file-icon">📄</div>
                            <div class="file-details">
                                <strong>@_selectedFile.Name</strong>
                                <span class="file-size">@FormatFileSize(_selectedFile.Size)</span>
                            </div>
                            <button type="button"
                                    class="remove-file-btn"
                                    @onclick="ClearSelectedFile">
                                ❌
                            </button>
                        </div>

                        <div class="import-actions">
                            <button type="button"
                                    class="import-button"
                                    @onclick="StartImport">
                                ⬆️ Import File
                            </button>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="processing-section">
                <div class="progress-container">
                    <h3>Processing KDBOM Files...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: @(_progress.Percentage)%"></div>
                    </div>
                    <div class="progress-info">
                        <span class="progress-text">@_progress.Stage</span>
                        <span class="progress-percentage">@_progress.Percentage%</span>
                    </div>
                    @if (!string.IsNullOrEmpty(_progress.CurrentFile))
                    {
                        <div class="current-file">
                            <strong>Current File:</strong> @_progress.CurrentFile
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(_progress.CurrentSheet))
                    {
                        <div class="current-sheet">
                            <strong>Current Sheet:</strong> @_progress.CurrentSheet
                        </div>
                    }
                </div>
            </div>
        }

        @if (_importResult != null)
        {
            <div class="result-section">
                @if (_importResult.Success)
                {
                    <div class="success-message">
                        <h3>✅ Import Successful</h3>
                        <div class="result-details">
                            <div class="result-item">
                                <span class="label">Files Processed:</span>
                                <span class="value">@_importResult.ProcessedFiles</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Total Records:</span>
                                <span class="value">@_importResult.TotalRecords.ToString("N0")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Model Info Records:</span>
                                <span class="value">@_importResult.ModelInfoRecords</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Parts Data Records:</span>
                                <span class="value">@_importResult.PartsDataRecords.ToString("N0")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Processing Time:</span>
                                <span class="value">@_importResult.ProcessingTime.ToString(@"mm\:ss")</span>
                            </div>
                        </div>

                        @if (_importResult.ProcessedSheets.Any())
                        {
                            <div class="processed-sheets">
                                <h4>Processed Sheets:</h4>
                                <ul>
                                    @foreach (var sheet in _importResult.ProcessedSheets)
                                    {
                                        <li>@sheet</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="error-message">
                        <h3>❌ Import Failed</h3>
                        <p>@_importResult.ErrorMessage</p>

                        @if (_importResult.Errors.Any())
                        {
                            <div class="error-details">
                                <h4>Errors:</h4>
                                <ul>
                                    @foreach (var error in _importResult.Errors)
                                    {
                                        <li>@error</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                }

                <div class="result-actions">
                    <button type="button"
                            class="view-data-button"
                            @onclick="NavigateToDataView">
                        👁️ View Imported Data
                    </button>
                    <button type="button"
                            class="new-import-button"
                            @onclick="ResetImport">
                        🔄 New Import
                    </button>
                </div>
            </div>
        }
    </div>

    <!-- Data Management Section (moved to bottom) -->
    @if (_hasExistingData && !(_clearResult?.Success == true) && !_isClearingData)
    {
        <div class="data-management-card">
            <div class="section-header">
                <h3>🗂️ Data Management</h3>
                <p class="section-description">Manage existing KDBOM data in the database</p>
            </div>

            <div class="clear-data-section">
                <div class="clear-data-info">
                    <p><strong>⚠️ Clear Existing Data:</strong> This will permanently delete all KDBOM data from the database.</p>
                    <p>Use this before importing new data if you want to replace all existing records.</p>
                </div>
                <button type="button"
                        class="clear-data-button"
                        @onclick="ShowClearConfirmation">
                    🗑️ Clear KDBOM Data
                </button>
            </div>
        </div>
    }
    else if (_isClearingData)
    {
        <div class="data-management-card">
            <div class="section-header">
                <h3>🗂️ Data Management</h3>
                <p class="section-description">Manage existing KDBOM data in the database</p>
            </div>
            
            <div class="clearing-section">
                <div class="clearing-progress">
                    <h4>🔄 Clearing KDBOM Data...</h4>
                    <p>Please wait while existing data is being removed from the database.</p>
                </div>
            </div>
        </div>
    }

    @if (_clearResult != null)
    {
        <div class="data-management-card">
            <div class="section-header">
                <h3>🗂️ Data Management</h3>
                <p class="section-description">Manage existing KDBOM data in the database</p>
            </div>
            
            <div class="clear-result-section">
                @if (_clearResult.Success)
                {
                    <div class="clear-success-message">
                        <h4>✅ Data Cleared Successfully</h4>
                        <div class="clear-result-details">
                            <div class="result-item">
                                <span class="label">Records Cleared:</span>
                                <span class="value">@_clearResult.ClearedRecords.ToString("N0")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Processing Time:</span>
                                <span class="value">@_clearResult.ProcessingTime.ToString(@"mm\:ss")</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Cleared By:</span>
                                <span class="value">@_clearResult.ClearedBy</span>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="clear-error-message">
                        <h4>❌ Clear Operation Failed</h4>
                        <p>@_clearResult.ErrorMessage</p>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Confirmation Dialog -->
    @if (_showClearConfirmation)
    {
        <div class="confirmation-overlay">
            <div class="confirmation-dialog">
                <div class="confirmation-header">
                    <h3>⚠️ Confirm Data Clearing</h3>
                </div>
                <div class="confirmation-content">
                    <p><strong>Are you sure you want to clear all KDBOM data?</strong></p>
                    <p>This action will permanently delete:</p>
                    <ul>
                        <li>All parts data records</li>
                        <li>All model information records</li>
                        <li>All file upload records</li>
                    </ul>
                    <p class="warning-text">⚠️ This action cannot be undone!</p>
                </div>
                <div class="confirmation-actions">
                    <button type="button" class="cancel-button" @onclick="CancelClearConfirmation">
                        Cancel
                    </button>
                    <button type="button" class="confirm-button" @onclick="ConfirmClearData">
                        Yes, Clear All Data
                    </button>
                </div>
            </div>
        </div>
    }

    <!-- Waiting Overlay for Data Operations -->
    @if (_isProcessing || _isClearingData)
    {
        <div class="waiting-overlay">
            <div class="waiting-dialog">
                <div class="waiting-content">
                    <div class="waiting-spinner">
                        <div class="spinner"></div>
                    </div>
                    <div class="waiting-text">
                        @if (_isProcessing)
                        {
                            <h3>🔄 Processing Data...</h3>
                            <p>Please wait while your KDBOM file is being processed and data is being inserted into the database.</p>
                            @if (!string.IsNullOrEmpty(_progress.Stage))
                            {
                                <div class="waiting-progress">
                                    <div class="progress-bar-small">
                                        <div class="progress-fill-small" style="width: @(_progress.Percentage)%"></div>
                                    </div>
                                    <p class="progress-stage">@_progress.Stage (@_progress.Percentage%)</p>
                                    @if (!string.IsNullOrEmpty(_progress.CurrentFile))
                                    {
                                        <p class="progress-file">File: @_progress.CurrentFile</p>
                                    }
                                    @if (!string.IsNullOrEmpty(_progress.CurrentSheet))
                                    {
                                        <p class="progress-sheet">Sheet: @_progress.CurrentSheet</p>
                                    }
                                </div>
                            }
                        }
                        else if (_isClearingData)
                        {
                            <h3>🗑️ Clearing Data...</h3>
                            <p>Please wait while existing KDBOM data is being removed from the database.</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private IBrowserFile? _selectedFile;
    private bool _isProcessing;
    private bool _isClearingData;
    private bool _showClearConfirmation;
    private bool _hasExistingData;
    private KDBOMUploadProgress _progress = new();
    private KDBOMUploadResult? _importResult;
    private KDBOMClearResult? _clearResult;
    private CancellationTokenSource? _cancellationTokenSource;

    protected override async Task OnInitializedAsync()
    {
        await CheckForExistingData();
    }

    private async Task CheckForExistingData()
    {
        try
        {
            _hasExistingData = await KDBOMService.HasKDBOMDataAsync();
        }
        catch
        {
            _hasExistingData = false;
        }
    }

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;

        // Validate file size
        if (file.Size > 200 * 1024 * 1024) // 200MB
        {
            await JSRuntime.InvokeVoidAsync("alert", $"File {file.Name} is too large. Maximum size is 200MB.");
            return;
        }

        _selectedFile = file;
        StateHasChanged();
    }

    private void ClearSelectedFile()
    {
        _selectedFile = null;
        StateHasChanged();
    }

    private void ShowClearConfirmation()
    {
        _showClearConfirmation = true;
        StateHasChanged();
    }

    private void CancelClearConfirmation()
    {
        _showClearConfirmation = false;
        StateHasChanged();
    }

    private async Task ConfirmClearData()
    {
        _showClearConfirmation = false;
        await ClearKDBOMData();
    }

    private async Task ClearKDBOMData()
    {
        _isClearingData = true;
        _clearResult = null;
        StateHasChanged();

        try
        {
            // Get current user (you may need to inject IHttpContextAccessor)
            var currentUser = "system"; // TODO: Get actual user from authentication

            _clearResult = await KDBOMService.ClearKDBOMDataAsync(currentUser);
            
            // Refresh data existence check after clearing
            await CheckForExistingData();
        }
        catch (Exception ex)
        {
            _clearResult = new KDBOMClearResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
        finally
        {
            _isClearingData = false;
            StateHasChanged();
        }
    }

    private async Task StartImport()
    {
        if (_selectedFile == null) return;

        _isProcessing = true;
        _importResult = null;
        _cancellationTokenSource = new CancellationTokenSource();
        StateHasChanged();

        try
        {
            var progress = new Progress<KDBOMUploadProgress>(p =>
            {
                _progress = p;
                InvokeAsync(StateHasChanged);
            });

            // Get current user (you may need to inject IHttpContextAccessor)
            var currentUser = "system"; // TODO: Get actual user from authentication

            // Process single file immediately to avoid JavaScript reference issues
            _importResult = await ProcessFileImmediately(_selectedFile, currentUser, progress);
            
            // Check if data exists after import operation
            await CheckForExistingData();
        }
        catch (Exception ex)
        {
            _importResult = new KDBOMUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
        finally
        {
            _isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task<KDBOMUploadResult> ProcessFileImmediately(
        IBrowserFile file,
        string uploadedBy,
        IProgress<KDBOMUploadProgress> progress)
    {
        var result = new KDBOMUploadResult();
        var tempFolderPath = Path.Combine(WebHostEnvironment.WebRootPath, "kdbom-temp");
        string? savedFilePath = null;

        try
        {
            // Create temp directory if it doesn't exist
            if (!Directory.Exists(tempFolderPath))
            {
                Directory.CreateDirectory(tempFolderPath);
            }

            // Save file to temp folder
            progress.Report(new KDBOMUploadProgress { Stage = "Saving file to temp folder...", Percentage = 5 });

            try
            {
                progress.Report(new KDBOMUploadProgress
                {
                    Stage = "Saving file",
                    Percentage = 10,
                    CurrentFile = file.Name
                });

                // Generate unique filename to avoid conflicts
                var fileName = $"{Guid.NewGuid()}_{file.Name}";
                var filePath = Path.Combine(tempFolderPath, fileName);

                await using var stream = file.OpenReadStream(200 * 1024 * 1024); // 200MB limit
                await using var fileStream = new FileStream(filePath, FileMode.Create);
                await stream.CopyToAsync(fileStream);

                savedFilePath = filePath;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"Error saving file {file.Name}: {ex.Message}";
                return result;
            }

            // Now process the saved file using the service (without clearing existing data)
            progress.Report(new KDBOMUploadProgress { Stage = "Processing saved file...", Percentage = 30 });
            result = await KDBOMService.ProcessKDBOMFilesFromPathsAsync(new List<string> { savedFilePath }, uploadedBy, clearExistingData: false, progress);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = $"Error in file processing: {ex.Message}";
        }
        finally
        {
            // Clean up temp file
            try
            {
                if (savedFilePath != null && File.Exists(savedFilePath))
                {
                    File.Delete(savedFilePath);
                }
            }
            catch (Exception ex)
            {
                // Log cleanup error but don't fail the operation
                Console.WriteLine($"Warning: Could not clean up temp file: {ex.Message}");
            }
        }

        return result;
    }

    private async Task NavigateToDataView()
    {
        await JSRuntime.InvokeVoidAsync("open", "/kdbom/data", "_blank");
    }

    private async Task ResetImport()
    {
        _selectedFile = null;
        _importResult = null;
        _clearResult = null;
        _progress = new KDBOMUploadProgress();
        
        // Check if data exists after import operation
        await CheckForExistingData();
        
        StateHasChanged();
    }

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB"];
        double len = bytes;
        var order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }

        return $"{len:0.##} {sizes[order]}";
    }

    public ValueTask DisposeAsync()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        return ValueTask.CompletedTask;
    }

}

<style>
    .kdbom-import-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .page-header h2 {
        color: var(--accent-fill-rest);
        margin: 0 0 0.5rem 0;
        font-size: 1.8rem;
    }

    .page-description {
        color: var(--neutral-foreground-rest);
        margin: 0;
        font-size: 0.95rem;
    }

    .data-management-card, .import-card {
        background: var(--neutral-layer-1);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
    }

    .section-header {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--neutral-stroke-rest);
    }

    .section-header h3 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.2rem;
    }

    .section-description {
        color: var(--neutral-foreground-hint);
        margin: 0;
        font-size: 0.9rem;
    }

    /* Data Management Section */
    .clear-data-section {
        padding: 1rem;
        background: var(--neutral-layer-2);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 6px;
        margin-bottom: 1rem;
    }

    .clear-data-info {
        margin-bottom: 1rem;
    }

    .clear-data-info p {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
    }

    .clear-data-button {
        background: #dc3545;
        color: white;
        border: 1px solid #dc3545;
        padding: 0.7rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .clear-data-button:hover {
        background: #c82333;
    }

    .clearing-section {
        text-align: center;
        padding: 2rem 1rem;
    }

    .clearing-progress h4 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
    }

    .clearing-progress p {
        margin: 0;
        color: var(--neutral-foreground-hint);
        font-size: 0.9rem;
    }

    .clear-result-section {
        margin-top: 1rem;
    }

    .clear-success-message {
        background: var(--fill-color-success);
        border: 1px solid var(--stroke-color-success);
        border-radius: 6px;
        padding: 1rem;
    }

    .clear-success-message h4 {
        margin: 0 0 0.8rem 0;
        color: var(--text-color-success);
        font-size: 1.1rem;
    }

    .clear-error-message {
        background: var(--fill-color-critical);
        border: 1px solid var(--stroke-color-critical);
        border-radius: 6px;
        padding: 1rem;
    }

    .clear-error-message h4 {
        margin: 0 0 0.8rem 0;
        color: var(--text-color-critical);
        font-size: 1.1rem;
    }

    .clear-result-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.6rem;
    }

    .file-selection-section h3 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.2rem;
    }

    .file-format-info {
        color: var(--neutral-foreground-hint);
        margin: 0 0 1rem 0;
        font-size: 0.9rem;
    }

    .file-input-wrapper {
        margin-bottom: 1.5rem;
    }

    .file-input-label {
        display: inline-block;
        cursor: pointer;
    }

    .file-input-hidden {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .browse-button {
        display: inline-block;
        background: var(--accent-fill-rest);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
        text-decoration: none;
    }

    .browse-button:hover {
        background: var(--accent-fill-hover);
    }

    .selected-file-section {
        border-top: 1px solid var(--neutral-stroke-rest);
        padding-top: 1rem;
    }

    .selected-file-section h4 {
        margin: 0 0 1rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.1rem;
    }

    /* Confirmation Dialog */
    .confirmation-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .confirmation-dialog {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    }

    .confirmation-header h3 {
        margin: 0 0 1rem 0;
        color: #dc3545;
        font-size: 1.2rem;
    }

    .confirmation-content {
        margin-bottom: 1.5rem;
    }

    .confirmation-content p {
        margin: 0 0 0.8rem 0;
        color: #333;
        font-size: 0.9rem;
    }

    .confirmation-content ul {
        margin: 0.5rem 0;
        padding-left: 1.2rem;
        color: #333;
        font-size: 0.9rem;
    }

    .confirmation-content li {
        margin-bottom: 0.3rem;
    }

    .warning-text {
        color: #dc3545 !important;
        font-weight: 600 !important;
    }

    .confirmation-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .cancel-button {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
    }

    .cancel-button:hover {
        background: #e9ecef;
    }

    .confirm-button {
        background: #dc3545;
        color: white;
        border: 1px solid #dc3545;
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .confirm-button:hover {
        background: #c82333;
    }

    .file-list {
        margin-bottom: 1rem;
    }

    .file-item {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        padding: 0.6rem;
        background: var(--neutral-layer-2);
        border: 1px solid var(--neutral-stroke-rest);
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }

    .file-icon {
        font-size: 1.2rem;
    }

    .file-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.2rem;
    }

    .file-details strong {
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
    }

    .file-size {
        color: var(--neutral-foreground-hint);
        font-size: 0.8rem;
    }

    .remove-file-btn {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.8rem;
        padding: 0.2rem;
        border-radius: 2px;
        transition: background-color 0.2s;
    }

    .remove-file-btn:hover {
        background: var(--neutral-fill-hover);
    }

    .import-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .clear-button {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
        border: 1px solid var(--neutral-stroke-rest);
        padding: 0.6rem 1.2rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
    }

    .clear-button:hover {
        background: var(--neutral-fill-hover);
    }

    .import-button {
        background: var(--accent-fill-rest);
        color: white;
        border: none;
        padding: 0.6rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .import-button:hover {
        background: var(--accent-fill-hover);
    }

    .processing-section {
        text-align: center;
        padding: 2rem 1rem;
    }

    .progress-container h3 {
        margin: 0 0 1.5rem 0;
        color: var(--neutral-foreground-rest);
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--neutral-fill-rest);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .progress-fill {
        height: 100%;
        background: var(--accent-fill-rest);
        transition: width 0.3s ease;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .progress-text {
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
    }

    .progress-percentage {
        color: var(--accent-fill-rest);
        font-weight: 600;
        font-size: 0.9rem;
    }

    .current-file, .current-sheet {
        color: var(--neutral-foreground-hint);
        font-size: 0.85rem;
        margin-bottom: 0.3rem;
    }

    .result-section {
        border-top: 1px solid var(--neutral-stroke-rest);
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }

    /* Waiting Overlay Styles */
    .waiting-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2000;
    }

    .waiting-dialog {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
    }

    .waiting-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .waiting-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--accent-fill-rest);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .waiting-text h3 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1.3rem;
    }

    .waiting-text p {
        margin: 0;
        color: var(--neutral-foreground-hint);
        font-size: 0.95rem;
        line-height: 1.4;
    }

    .waiting-progress {
        margin-top: 1rem;
        width: 100%;
    }

    .progress-bar-small {
        width: 100%;
        height: 6px;
        background: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.8rem;
    }

    .progress-fill-small {
        height: 100%;
        background: var(--accent-fill-rest);
        transition: width 0.3s ease;
    }

    .progress-stage {
        margin: 0 0 0.4rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 0.9rem;
        font-weight: 500;
    }

    .progress-file, .progress-sheet {
        margin: 0 0 0.2rem 0;
        color: var(--neutral-foreground-hint);
        font-size: 0.85rem;
    }

    .success-message {
        background: var(--fill-color-success);
        border: 1px solid var(--stroke-color-success);
        border-radius: 6px;
        padding: 1.2rem;
        margin-bottom: 1rem;
    }

    .success-message h3 {
        margin: 0 0 1rem 0;
        color: var(--text-color-success);
        font-size: 1.2rem;
    }

    .error-message {
        background: var(--fill-color-critical);
        border: 1px solid var(--stroke-color-critical);
        border-radius: 6px;
        padding: 1.2rem;
        margin-bottom: 1rem;
    }

    .error-message h3 {
        margin: 0 0 1rem 0;
        color: var(--text-color-critical);
        font-size: 1.2rem;
    }

    .result-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.8rem;
        margin-bottom: 1rem;
    }

    .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.4rem 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .result-item .label {
        font-weight: 500;
        color: var(--neutral-foreground-rest);
    }

    .result-item .value {
        font-weight: 600;
        color: var(--accent-fill-rest);
    }

    .processed-sheets {
        margin-top: 1rem;
    }

    .processed-sheets h4 {
        margin: 0 0 0.5rem 0;
        color: var(--neutral-foreground-rest);
        font-size: 1rem;
    }

    .processed-sheets ul {
        margin: 0;
        padding-left: 1.2rem;
        color: var(--neutral-foreground-hint);
        font-size: 0.9rem;
    }

    .processed-sheets li {
        margin-bottom: 0.2rem;
    }

    .error-details {
        margin-top: 1rem;
    }

    .error-details h4 {
        margin: 0 0 0.5rem 0;
        color: var(--text-color-critical);
        font-size: 1rem;
    }

    .error-details ul {
        margin: 0;
        padding-left: 1.2rem;
        color: var(--text-color-critical);
        font-size: 0.9rem;
    }

    .error-details li {
        margin-bottom: 0.3rem;
    }

    .result-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 1.5rem;
    }

    .view-data-button, .new-import-button {
        padding: 0.7rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 500;
        transition: background-color 0.2s;
        text-decoration: none;
        border: none;
    }

    .view-data-button {
        background: var(--accent-fill-rest);
        color: white;
    }

    .view-data-button:hover {
        background: var(--accent-fill-hover);
    }

    .new-import-button {
        background: var(--neutral-fill-rest);
        color: var(--neutral-foreground-rest);
        border: 1px solid var(--neutral-stroke-rest);
    }

    .new-import-button:hover {
        background: var(--neutral-fill-hover);
    }

    @@media (max-width: 768px) {
        .kdbom-import-container {
            padding: 0.5rem;
        }

        .data-management-card, .import-card {
            padding: 1rem;
        }

        .file-input-wrapper {
            flex-direction: column;
            align-items: stretch;
        }

        .import-actions {
            flex-direction: column;
        }

        .result-details, .clear-result-details {
            grid-template-columns: 1fr;
        }

        .result-actions {
            flex-direction: column;
        }

        .confirmation-dialog {
            width: 95%;
            padding: 1rem;
        }

        .confirmation-actions {
            flex-direction: column;
        }
    }
</style>