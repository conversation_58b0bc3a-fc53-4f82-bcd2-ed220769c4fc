using System.ComponentModel.DataAnnotations;

namespace ProjectECN.DTO
{
    /// <summary>
    /// Data Transfer Object for ECN Parts Information
    /// Contains parts data from ECN and matching KDBOM information
    /// </summary>
    public class ECNPartsInfoDto
    {
        /// <summary>
        /// ECN Number
        /// </summary>
        [Required]
        public string EcnNumber { get; set; } = string.Empty;

        /// <summary>
        /// List of parts associated with this ECN
        /// </summary>
        public List<ECNPartDetailDto> Parts { get; set; } = new();

        /// <summary>
        /// Total number of parts found for this ECN
        /// </summary>
        public int TotalParts { get; set; }

        /// <summary>
        /// Number of parts found in KDBOM
        /// </summary>
        public int PartsFoundInKDBOM { get; set; }

        /// <summary>
        /// ECN creation date
        /// </summary>
        public DateTime? CreatedDate { get; set; }
    }

    /// <summary>
    /// Individual part detail with KDBOM matching information
    /// </summary>
    public class ECNPartDetailDto
    {
        /// <summary>
        /// Part number from ECN
        /// </summary>
        public string PartNumber { get; set; } = string.Empty;

        /// <summary>
        /// Old part number from ECN
        /// </summary>
        public string OldPartNumber { get; set; } = string.Empty;

        /// <summary>
        /// New part number from ECN
        /// </summary>
        public string NewPartNumber { get; set; } = string.Empty;

        /// <summary>
        /// Part name from ECN
        /// </summary>
        public string PartName { get; set; } = string.Empty;

        /// <summary>
        /// BOM Group Name from ECN
        /// </summary>
        public string BomGname { get; set; } = string.Empty;

        /// <summary>
        /// Page number from ECN
        /// </summary>
        public string Page { get; set; } = string.Empty;

        /// <summary>
        /// Line number from ECN
        /// </summary>
        public string Line { get; set; } = string.Empty;

        /// <summary>
        /// Block information from ECN
        /// </summary>
        public string Block { get; set; } = string.Empty;

        /// <summary>
        /// Drawing number from ECN
        /// </summary>
        public string DwgNumber { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if this part was found in KDBOM
        /// </summary>
        public bool FoundInKDBOM { get; set; }

        /// <summary>
        /// KDBOM matching information (if found)
        /// </summary>
        public KDBOMMatchInfo? KDBOMMatch { get; set; }
    }

    /// <summary>
    /// KDBOM matching information for a part
    /// </summary>
    public class KDBOMMatchInfo
    {
        /// <summary>
        /// Model code from KDBOM
        /// </summary>
        public string ModelCode { get; set; } = string.Empty;

        /// <summary>
        /// Model name from KDBOM ModelInfo
        /// </summary>
        public string ModelName { get; set; } = string.Empty;

        /// <summary>
        /// Send Type from KDBOM
        /// </summary>
        public string SendType { get; set; } = string.Empty;

        /// <summary>
        /// BOM Group Name from KDBOM
        /// </summary>
        public string BOMGName { get; set; } = string.Empty;

        /// <summary>
        /// Dealer information from KDBOM
        /// </summary>
        public string Dealer { get; set; } = string.Empty;

        /// <summary>
        /// Quantity from KDBOM
        /// </summary>
        public string QTY { get; set; } = string.Empty;

        /// <summary>
        /// Install location comment from KDBOM
        /// </summary>
        public string InstallLocationComment { get; set; } = string.Empty;

        /// <summary>
        /// Sheet name where this part was found in KDBOM
        /// </summary>
        public string SheetName { get; set; } = string.Empty;

        /// <summary>
        /// File name where this part was found in KDBOM
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if Send Type is marked (not empty/null)
        /// </summary>
        public bool IsSendTypeMarked => !string.IsNullOrWhiteSpace(SendType);
    }
}
