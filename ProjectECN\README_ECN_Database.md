# ECN Database Setup and Usage Guide

This document explains how to set up and use the ECN (Engineering Change Notice) database for the ProjectECN application.

## Database Structure

The ECN system uses a SQL Server database with the following structure:

### Database: `ECN`
### Schema: `raw`
### Main Table: `raw.RawECNData`

## Setup Instructions

### 1. Create the Database and Tables

Run the SQL script to create the database structure:

```sql
-- Execute this script in SQL Server Management Studio or Azure Data Studio
-- File: ProjectECN/SQL/CreateECNTables.sql
```

This script will:
- Create the `ECN` database (if it doesn't exist)
- Create the `raw` schema
- Create the `RawECNData` table with all required columns
- Create performance indexes
- Create helpful views and stored procedures

### 2. Test the Setup

After creating the tables, run the test script to verify everything works:

```sql
-- Execute this script to test the database setup
-- File: ProjectECN/SQL/TestECNTables.sql
```

### 3. Update Connection String

Ensure your `appsettings.json` has the correct connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(local);Database=ECN;Integrated Security=True;TrustServerCertificate=True"
  }
}
```

## Database Schema Details

### Main Table: `raw.RawECNData`

| Column | Type | Description |
|--------|------|-------------|
| `id` | UNIQUEIDENTIFIER | Primary key (auto-generated) |
| `file_name` | VARCHAR(600) | Original Excel file name |
| `created_date` | DATETIME | Date extracted from folder structure |
| `ecn_no` | VARCHAR(20) | ECN number (e.g., 52U-0270) |
| `cc_code` | VARCHAR(50) | Company/Cost Center code |
| `prod_type` | VARCHAR(20) | Product type (2W, 4W, etc.) |
| `bom_gname` | VARCHAR(40) | BOM group name |
| `page` | VARCHAR(200) | Page information |
| `line` | VARCHAR(10) | Line number |
| `block` | VARCHAR(10) | Block identifier |
| `old_part_number` | VARCHAR(50) | Original part number |
| `new_part_number` | VARCHAR(50) | New part number |
| `part_name` | VARCHAR(500) | Part description |
| `dwg_number` | VARCHAR(100) | Drawing number |
| `dwg_ecn_no` | VARCHAR(50) | Drawing ECN number |
| `belongs_to_psmc` | BIT | Whether ECN belongs to PSMC factory |
| `record_created_date` | DATETIME | When record was inserted |
| `record_created_by` | VARCHAR(50) | User who created the record |
| `record_modified_date` | DATETIME | Last modification date |
| `record_modified_by` | VARCHAR(50) | User who last modified |

### Performance Indexes

- `IX_RawECNData_CreatedDate` - For date-based queries
- `IX_RawECNData_ECNNo` - For ECN number lookups
- `IX_RawECNData_PSMC` - For PSMC-specific queries
- `IX_RawECNData_PartNumbers` - For part number searches

### Views

#### `raw.vw_ECNDataSummary`
Provides daily summary statistics:
- Total records per date
- Unique ECN count
- PSMC records count
- Unique CC codes and product types

#### `raw.vw_PSMCECNData`
Filtered view showing only PSMC-related ECN data.

### Stored Procedures

#### `raw.sp_DeleteECNDataByDate`
Deletes all ECN records for a specific date.

**Usage:**
```sql
EXEC raw.sp_DeleteECNDataByDate @CreatedDate = '2025-01-27'
```

#### `raw.sp_GetECNStatistics`
Returns statistics for a date range.

**Usage:**
```sql
EXEC raw.sp_GetECNStatistics @StartDate = '2025-01-01', @EndDate = '2025-01-31'
```

## Application Integration

### Entity Framework Configuration

The application uses Entity Framework Core with:
- `ApplicationDbContext` for database access
- `RawEcndatum` entity model
- `ECNDatabaseService` for data operations

### Key Services

- **ECNUploadService**: Handles file upload and processing
- **ECNDatabaseService**: Database operations (save, delete, query)
- **ECNExcelProcessingService**: Excel file processing
- **ECNImageProcessingService**: TIFF image processing
- **ECNFactoryOcrService**: OCR for factory detection

## Usage Examples

### Query ECN Data by Date
```sql
SELECT * FROM raw.RawECNData 
WHERE CAST(created_date AS DATE) = '2025-01-27'
ORDER BY ecn_no
```

### Get PSMC ECNs Only
```sql
SELECT * FROM raw.vw_PSMCECNData 
WHERE CAST(created_date AS DATE) = '2025-01-27'
```

### Get Daily Statistics
```sql
SELECT * FROM raw.vw_ECNDataSummary 
WHERE created_date >= '2025-01-01'
ORDER BY created_date DESC
```

## File Upload Process

1. User uploads YYYYMMDD.zip file via web interface
2. System extracts ZIP to `/wwwroot/enc/YYYYMMDD/` folder
3. TIFF images are processed to extract ECN description and factory boxes
4. Factory images undergo OCR to detect PSMC qualification
5. Excel files are processed and data is saved to `raw.RawECNData`
6. PSMC ECN data is also saved to JSON file for quick access

## Troubleshooting

### Common Issues

1. **File Upload Errors**: Check file permissions and disk space
2. **Database Connection**: Verify connection string and SQL Server access
3. **OCR Issues**: Ensure Tesseract is properly installed
4. **Excel Processing**: Check EPPlus license configuration

### Re-upload Handling

The system supports re-uploading the same date:
- Existing folder and database records are deleted
- New data is processed and saved
- No duplicate data issues

## Maintenance

### Regular Tasks

1. **Backup Database**: Regular backups of ECN database
2. **Clean Old Files**: Remove old extracted files from `/wwwroot/enc/`
3. **Monitor Disk Space**: ECN files and images can consume significant space
4. **Index Maintenance**: Rebuild indexes periodically for performance

### Performance Monitoring

Use the statistics stored procedure to monitor:
- Daily upload volumes
- Processing success rates
- PSMC qualification rates
- Data growth trends
