using System.Diagnostics;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using ProjectECN.DTO.KDBOM;
using ProjectECN.Models;
using ProjectECN.Models.KDBOM;

namespace ProjectECN.Services;

public class KDBOMService(
    IDbContextFactory<ApplicationDbContext> contextFactory,
    ILogger<KDBOMService> logger,
    IWebHostEnvironment environment)
{
    private readonly IWebHostEnvironment _environment = environment;
    
    /// <summary>
    /// Checks if any KDBOM data exists in the database
    /// </summary>
    /// <returns>True if any KDBOM data exists, false otherwise</returns>
    public async Task<bool> HasKDBOMDataAsync()
    {
        await using var context = await contextFactory.CreateDbContextAsync();
        return await context.KDBOMFileUploads.AnyAsync() || 
               await context.KDBOMModelInfos.AnyAsync() || 
               await context.KDBOMPartsData.AnyAsync();
    }

    /// <summary>
    ///     Process multiple KDBOM Excel files and import to database
    /// </summary>
    public async Task<KDBOMUploadResult> ProcessKDBOMFilesAsync(
        List<IBrowserFile> files,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KDBOMUploadResult();

        try
        {
            logger.LogInformation($"Starting KDBOM processing for {files.Count} files by user: {uploadedBy}");

            // Clear existing data first
            await ClearExistingKDBOMDataAsync();
            progress?.Report(new KDBOMUploadProgress { Stage = "Cleared existing data", Percentage = 5 });

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;
            var processedSheets = new List<string>();

            for (var i = 0; i < files.Count; i++)
            {
                var file = files[i];
                var fileProgress = i * 90 / files.Count + 10; // 10-100% range

                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing file {i + 1} of {files.Count}",
                    Percentage = fileProgress,
                    CurrentFile = file.Name
                });

                try
                {
                    var fileResult = await ProcessSingleKDBOMFileAsync(file, uploadedBy, progress);
                    if (fileResult.Success)
                    {
                        result.ProcessedFiles++;
                        totalRecords += fileResult.TotalRecords;
                        modelInfoRecords += fileResult.ModelInfoRecords;
                        partsDataRecords += fileResult.PartsDataRecords;
                        processedSheets.AddRange(fileResult.ProcessedSheets);
                    }
                    else
                    {
                        result.Errors.Add($"File {file.Name}: {fileResult.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Error processing file {file.Name}");
                    result.Errors.Add($"File {file.Name}: {ex.Message}");
                }
            }

            result.Success = result.ProcessedFiles > 0;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;
            result.ProcessedSheets = processedSheets;
            result.ProcessingTime = stopwatch.Elapsed;

            if (result.Success)
            {
                result.Message =
                    $"Successfully processed {result.ProcessedFiles} files with {result.TotalRecords} total records";
                logger.LogInformation(result.Message);
            }
            else
            {
                result.ErrorMessage = "No files were processed successfully";
                logger.LogWarning(result.ErrorMessage);
            }

            progress?.Report(new KDBOMUploadProgress { Stage = "Processing completed", Percentage = 100 });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in KDBOM processing");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Process KDBOM files from file paths (uses EPPlus to read from disk)
    /// </summary>
    public async Task<KDBOMUploadResult> ProcessKDBOMFilesFromPathsAsync(
        List<string> filePaths,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        return await ProcessKDBOMFilesFromPathsAsync(filePaths, uploadedBy, true, progress);
    }

    /// <summary>
    ///     Process KDBOM files from file paths with option to clear existing data
    /// </summary>
    public async Task<KDBOMUploadResult> ProcessKDBOMFilesFromPathsAsync(
        List<string> filePaths,
        string uploadedBy,
        bool clearExistingData,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KDBOMUploadResult();

        try
        {
            logger.LogInformation(
                $"Starting KDBOM processing for {filePaths.Count} files by user: {uploadedBy} (Clear existing: {clearExistingData})");

            var startPercentage = 5;
            if (clearExistingData)
            {
                // Clear existing data first
                await ClearExistingKDBOMDataAsync();
                progress?.Report(new KDBOMUploadProgress { Stage = "Cleared existing data", Percentage = 5 });
            }
            else
            {
                progress?.Report(new KDBOMUploadProgress
                    { Stage = "Starting import (appending to existing data)", Percentage = 5 });
            }

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;
            var processedSheets = new List<string>();

            for (var i = 0; i < filePaths.Count; i++)
            {
                var filePath = filePaths[i];
                var fileName = Path.GetFileName(filePath);
                var fileProgress = startPercentage + i * 90 / filePaths.Count; // 5-95% range

                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing file {i + 1} of {filePaths.Count}",
                    Percentage = fileProgress,
                    CurrentFile = fileName
                });

                try
                {
                    var fileResult = await ProcessSingleKDBOMFileFromPathAsync(filePath, uploadedBy, progress);
                    if (fileResult.Success)
                    {
                        result.ProcessedFiles++;
                        totalRecords += fileResult.TotalRecords;
                        modelInfoRecords += fileResult.ModelInfoRecords;
                        partsDataRecords += fileResult.PartsDataRecords;
                        processedSheets.AddRange(fileResult.ProcessedSheets);
                    }
                    else
                    {
                        result.Errors.Add($"File {fileName}: {fileResult.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Error processing file {fileName}");
                    result.Errors.Add($"File {fileName}: {ex.Message}");
                }
            }

            result.Success = result.ProcessedFiles > 0;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;
            result.ProcessedSheets = processedSheets;
            result.ProcessingTime = stopwatch.Elapsed;

            if (result.Success)
            {
                var action = clearExistingData ? "replaced" : "appended";
                result.Message =
                    $"Successfully processed {result.ProcessedFiles} files with {result.TotalRecords} total records ({action} data)";
                logger.LogInformation(result.Message);
            }
            else
            {
                result.ErrorMessage = "No files were processed successfully";
                logger.LogWarning(result.ErrorMessage);
            }

            progress?.Report(new KDBOMUploadProgress { Stage = "Processing completed", Percentage = 100 });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in KDBOM processing");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Process KDBOM files from byte array data (avoids JavaScript reference issues)
    /// </summary>
    public async Task<KDBOMUploadResult> ProcessKDBOMFileDataAsync(
        List<(string FileName, byte[] Data)> fileDataList,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KDBOMUploadResult();

        try
        {
            logger.LogInformation($"Starting KDBOM processing for {fileDataList.Count} files by user: {uploadedBy}");

            // Clear existing data first
            await ClearExistingKDBOMDataAsync();
            progress?.Report(new KDBOMUploadProgress { Stage = "Cleared existing data", Percentage = 35 });

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;
            var processedSheets = new List<string>();

            for (var i = 0; i < fileDataList.Count; i++)
            {
                var (fileName, fileData) = fileDataList[i];
                var fileProgress = 35 + i * 60 / fileDataList.Count; // 35-95% range

                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing file {i + 1} of {fileDataList.Count}",
                    Percentage = fileProgress,
                    CurrentFile = fileName
                });

                try
                {
                    var fileResult = await ProcessSingleKDBOMFileDataAsync(fileName, fileData, uploadedBy, progress);
                    if (fileResult.Success)
                    {
                        result.ProcessedFiles++;
                        totalRecords += fileResult.TotalRecords;
                        modelInfoRecords += fileResult.ModelInfoRecords;
                        partsDataRecords += fileResult.PartsDataRecords;
                        processedSheets.AddRange(fileResult.ProcessedSheets);
                    }
                    else
                    {
                        result.Errors.Add($"File {fileName}: {fileResult.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Error processing file {fileName}");
                    result.Errors.Add($"File {fileName}: {ex.Message}");
                }
            }

            result.Success = result.ProcessedFiles > 0;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;
            result.ProcessedSheets = processedSheets;
            result.ProcessingTime = stopwatch.Elapsed;

            if (result.Success)
            {
                result.Message =
                    $"Successfully processed {result.ProcessedFiles} files with {result.TotalRecords} total records";
                logger.LogInformation(result.Message);
            }
            else
            {
                result.ErrorMessage = "No files were processed successfully";
                logger.LogWarning(result.ErrorMessage);
            }

            progress?.Report(new KDBOMUploadProgress { Stage = "Processing completed", Percentage = 100 });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in KDBOM processing");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Process a single KDBOM Excel file
    /// </summary>
    private async Task<KDBOMUploadResult> ProcessSingleKDBOMFileAsync(
        IBrowserFile file,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var result = new KDBOMUploadResult();

        await using var context = await contextFactory.CreateDbContextAsync();
        using var transaction = await context.Database.BeginTransactionAsync();

        try
        {
            // Create file upload record
            var fileUpload = new FileUpload
            {
                FileName = Path.GetFileNameWithoutExtension(file.Name),
                OriginalFileName = file.Name,
                FilePath = $"kdbom/{file.Name}",
                FileSize = file.Size,
                UploadedBy = uploadedBy,
                CreatedBy = uploadedBy,
                ProcessingStatus = "Processing"
            };

            context.KDBOMFileUploads.Add(fileUpload);
            await context.SaveChangesAsync();

            // Process Excel file - increase size limit to 200MB and use memory stream to avoid synchronous read issues
            const long maxFileSize = 200 * 1024 * 1024; // 200MB limit
            using var memoryStream = new MemoryStream();
            await file.OpenReadStream(maxFileSize).CopyToAsync(memoryStream);
            memoryStream.Position = 0;
            using var package = new ExcelPackage(memoryStream);

            var worksheets = package.Workbook.Worksheets;
            if (worksheets.Count == 0) throw new InvalidOperationException("No worksheets found in the Excel file");

            logger.LogInformation($"Found {worksheets.Count} worksheets in file {file.Name}");

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;

            // Process first sheet as model info
            var firstSheet = worksheets[0];
            progress?.Report(new KDBOMUploadProgress
            {
                Stage = "Processing model information",
                CurrentSheet = firstSheet.Name
            });

            // Collect all data first, then bulk insert
            var allModelInfos = new List<ModelInfo>();
            var allPartsData = new List<PartsData>();

            var modelInfo = ProcessModelInfoSheet(firstSheet, fileUpload.Id, uploadedBy);
            if (modelInfo != null)
            {
                allModelInfos.Add(modelInfo);
                modelInfoRecords++;
                result.ProcessedSheets.Add($"{firstSheet.Name} (Model Info)");
            }

            // Process remaining sheets as parts data
            for (var i = 1; i < worksheets.Count; i++)
            {
                var worksheet = worksheets[i];
                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing parts data sheet {i}",
                    CurrentSheet = worksheet.Name
                });

                var partsData = ProcessPartsDataSheet(worksheet, fileUpload.Id, uploadedBy);
                if (partsData.Any())
                {
                    allPartsData.AddRange(partsData);
                    partsDataRecords += partsData.Count;
                    result.ProcessedSheets.Add($"{worksheet.Name} ({partsData.Count} parts)");
                    logger.LogInformation($"Processed sheet {worksheet.Name} with {partsData.Count} parts");
                }
                else
                {
                    logger.LogWarning($"No parts data found in sheet {worksheet.Name}");
                }
            }

            totalRecords = modelInfoRecords + partsDataRecords;

            // Bulk insert all data at once for better performance
            progress?.Report(new KDBOMUploadProgress
            {
                Stage = "Saving data to database...",
                CurrentSheet = "Bulk Insert"
            });

            if (allModelInfos.Any())
            {
                await BulkInsertModelInfoAsync(context, allModelInfos);
                logger.LogInformation($"Bulk inserted {allModelInfos.Count} model info records");
            }

            if (allPartsData.Any())
            {
                await BulkInsertPartsDataAsync(context, allPartsData);
                logger.LogInformation($"Bulk inserted {allPartsData.Count} parts data records");
            }

            // Update file upload record
            fileUpload.ProcessedDate = DateTime.Now;
            fileUpload.ProcessingStatus = "Completed";
            fileUpload.RecordCount = totalRecords;

            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            result.Success = true;
            result.ProcessedFiles = 1;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;

            logger.LogInformation($"Successfully processed file {file.Name} with {totalRecords} records");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, $"Error processing file {file.Name}");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Process a single KDBOM Excel file from file path (uses EPPlus to read from disk)
    /// </summary>
    private async Task<KDBOMUploadResult> ProcessSingleKDBOMFileFromPathAsync(
        string filePath,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var result = new KDBOMUploadResult();
        var fileName = Path.GetFileName(filePath);

        await using var context = await contextFactory.CreateDbContextAsync();
        using var transaction = await context.Database.BeginTransactionAsync();

        try
        {
            // Get file info
            var fileInfo = new FileInfo(filePath);

            // Create file upload record
            var fileUpload = new FileUpload
            {
                FileName = Path.GetFileNameWithoutExtension(fileName),
                OriginalFileName = fileName,
                FilePath = $"kdbom/{fileName}",
                FileSize = fileInfo.Length,
                UploadedBy = uploadedBy,
                CreatedBy = uploadedBy,
                ProcessingStatus = "Processing"
            };

            context.KDBOMFileUploads.Add(fileUpload);
            await context.SaveChangesAsync();

            // Process Excel file directly from file path using EPPlus
            ExcelPackage package;
            try
            {
                // Try to open the file directly
                package = new ExcelPackage(new FileInfo(filePath));
            }
            catch (Exception ex) when (ex.Message.Contains("encryption") || ex.Message.Contains("EncryptionInfo"))
            {
                // If encryption error, try opening as a stream with different approach
                logger.LogWarning($"Encryption error with file {fileName}, trying alternative approach: {ex.Message}");

                try
                {
                    // Read file into memory stream and try again
                    var fileBytes = await File.ReadAllBytesAsync(filePath);
                    var memoryStream = new MemoryStream(fileBytes);
                    package = new ExcelPackage(memoryStream);
                }
                catch (Exception streamEx)
                {
                    logger.LogError(streamEx, $"Failed to open file {fileName} with alternative approach");
                    throw new InvalidOperationException(
                        $"Unable to read Excel file {fileName}. The file may be corrupted, password-protected, or in an unsupported format. Original error: {ex.Message}");
                }
            }

            using (package)
            {
                var worksheets = package.Workbook.Worksheets;
                if (worksheets.Count == 0) throw new InvalidOperationException("No worksheets found in the Excel file");

                logger.LogInformation($"Found {worksheets.Count} worksheets in file {fileName}");

                var totalRecords = 0;
                var modelInfoRecords = 0;
                var partsDataRecords = 0;

                // Process first sheet as model info
                var firstSheet = worksheets[0];
                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = "Processing model information",
                    CurrentSheet = firstSheet.Name
                });

                // Collect all data first, then bulk insert
                var allModelInfos = new List<ModelInfo>();
                var allPartsData = new List<PartsData>();

                var modelInfo = ProcessModelInfoSheet(firstSheet, fileUpload.Id, uploadedBy);
                if (modelInfo != null)
                {
                    allModelInfos.Add(modelInfo);
                    modelInfoRecords++;
                    result.ProcessedSheets.Add($"{firstSheet.Name} (Model Info)");
                }

                // Process remaining sheets as parts data
                for (var i = 1; i < worksheets.Count; i++)
                {
                    var worksheet = worksheets[i];
                    progress?.Report(new KDBOMUploadProgress
                    {
                        Stage = $"Processing parts data sheet {i}",
                        CurrentSheet = worksheet.Name
                    });

                    var partsData = ProcessPartsDataSheet(worksheet, fileUpload.Id, uploadedBy);
                    if (partsData.Any())
                    {
                        allPartsData.AddRange(partsData);
                        partsDataRecords += partsData.Count;
                        result.ProcessedSheets.Add($"{worksheet.Name} ({partsData.Count} parts)");
                        logger.LogInformation($"Processed sheet {worksheet.Name} with {partsData.Count} parts");
                    }
                    else
                    {
                        logger.LogWarning($"No parts data found in sheet {worksheet.Name}");
                    }
                }

                totalRecords = modelInfoRecords + partsDataRecords;

                // Bulk insert all data at once for better performance
                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = "Saving data to database...",
                    CurrentSheet = "Bulk Insert"
                });

                if (allModelInfos.Any())
                {
                    await BulkInsertModelInfoAsync(context, allModelInfos);
                    logger.LogInformation($"Bulk inserted {allModelInfos.Count} model info records");
                }

                if (allPartsData.Any())
                {
                    await BulkInsertPartsDataAsync(context, allPartsData);
                    logger.LogInformation($"Bulk inserted {allPartsData.Count} parts data records");
                }

                // Update file upload record
                fileUpload.ProcessedDate = DateTime.Now;
                fileUpload.ProcessingStatus = "Completed";
                fileUpload.RecordCount = totalRecords;

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                result.Success = true;
                result.ProcessedFiles = 1;
                result.TotalRecords = totalRecords;
                result.ModelInfoRecords = modelInfoRecords;
                result.PartsDataRecords = partsDataRecords;

                logger.LogInformation($"Successfully processed file {fileName} with {totalRecords} records");
            }
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, $"Error processing file {fileName}");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Process a single KDBOM Excel file from byte array data
    /// </summary>
    private async Task<KDBOMUploadResult> ProcessSingleKDBOMFileDataAsync(
        string fileName,
        byte[] fileData,
        string uploadedBy,
        IProgress<KDBOMUploadProgress>? progress = null)
    {
        var result = new KDBOMUploadResult();

        await using var context = await contextFactory.CreateDbContextAsync();
        using var transaction = await context.Database.BeginTransactionAsync();

        try
        {
            // Create file upload record
            var fileUpload = new FileUpload
            {
                FileName = Path.GetFileNameWithoutExtension(fileName),
                OriginalFileName = fileName,
                FilePath = $"kdbom/{fileName}",
                FileSize = fileData.Length,
                UploadedBy = uploadedBy,
                CreatedBy = uploadedBy,
                ProcessingStatus = "Processing"
            };

            context.KDBOMFileUploads.Add(fileUpload);
            await context.SaveChangesAsync();

            // Process Excel file from byte array
            using var memoryStream = new MemoryStream(fileData);
            using var package = new ExcelPackage(memoryStream);

            var worksheets = package.Workbook.Worksheets;
            if (worksheets.Count == 0) throw new InvalidOperationException("No worksheets found in the Excel file");

            logger.LogInformation($"Found {worksheets.Count} worksheets in file {fileName}");

            var totalRecords = 0;
            var modelInfoRecords = 0;
            var partsDataRecords = 0;

            // Process first sheet as model info
            var firstSheet = worksheets[0];
            progress?.Report(new KDBOMUploadProgress
            {
                Stage = "Processing model information",
                CurrentSheet = firstSheet.Name
            });

            // Collect all data first, then bulk insert
            var allModelInfos = new List<ModelInfo>();
            var allPartsData = new List<PartsData>();

            var modelInfo = ProcessModelInfoSheet(firstSheet, fileUpload.Id, uploadedBy);
            if (modelInfo != null)
            {
                allModelInfos.Add(modelInfo);
                modelInfoRecords++;
                result.ProcessedSheets.Add($"{firstSheet.Name} (Model Info)");
            }

            // Process remaining sheets as parts data
            for (var i = 1; i < worksheets.Count; i++)
            {
                var worksheet = worksheets[i];
                progress?.Report(new KDBOMUploadProgress
                {
                    Stage = $"Processing parts data sheet {i}",
                    CurrentSheet = worksheet.Name
                });

                var partsData = ProcessPartsDataSheet(worksheet, fileUpload.Id, uploadedBy);
                if (partsData.Any())
                {
                    allPartsData.AddRange(partsData);
                    partsDataRecords += partsData.Count;
                    result.ProcessedSheets.Add($"{worksheet.Name} ({partsData.Count} parts)");
                    logger.LogInformation($"Processed sheet {worksheet.Name} with {partsData.Count} parts");
                }
                else
                {
                    logger.LogWarning($"No parts data found in sheet {worksheet.Name}");
                }
            }

            totalRecords = modelInfoRecords + partsDataRecords;

            // Bulk insert all data at once for better performance
            progress?.Report(new KDBOMUploadProgress
            {
                Stage = "Saving data to database...",
                CurrentSheet = "Bulk Insert"
            });

            if (allModelInfos.Any())
            {
                await BulkInsertModelInfoAsync(context, allModelInfos);
                logger.LogInformation($"Bulk inserted {allModelInfos.Count} model info records");
            }

            if (allPartsData.Any())
            {
                await BulkInsertPartsDataAsync(context, allPartsData);
                logger.LogInformation($"Bulk inserted {allPartsData.Count} parts data records");
            }

            // Update file upload record
            fileUpload.ProcessedDate = DateTime.Now;
            fileUpload.ProcessingStatus = "Completed";
            fileUpload.RecordCount = totalRecords;

            await context.SaveChangesAsync();
            await transaction.CommitAsync();

            result.Success = true;
            result.ProcessedFiles = 1;
            result.TotalRecords = totalRecords;
            result.ModelInfoRecords = modelInfoRecords;
            result.PartsDataRecords = partsDataRecords;

            logger.LogInformation($"Successfully processed file {fileName} with {totalRecords} records");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, $"Error processing file {fileName}");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    ///     Bulk insert model info records using EFCore.BulkExtensions for better performance
    /// </summary>
    private async Task BulkInsertModelInfoAsync(ApplicationDbContext context, List<ModelInfo> modelInfos)
    {
        if (!modelInfos.Any()) return;

        var bulkConfig = new BulkConfig
        {
            BatchSize = 1000,
            BulkCopyTimeout = 300, // 5 minutes timeout
            EnableStreaming = true,
            UseTempDB = true
        };

        await context.BulkInsertAsync(modelInfos, bulkConfig);
    }

    /// <summary>
    ///     Bulk insert parts data records using EFCore.BulkExtensions for better performance
    /// </summary>
    private async Task BulkInsertPartsDataAsync(ApplicationDbContext context, List<PartsData> partsDataList)
    {
        if (!partsDataList.Any()) return;

        var bulkConfig = new BulkConfig
        {
            BatchSize = 1000,
            BulkCopyTimeout = 300, // 5 minutes timeout
            EnableStreaming = true,
            UseTempDB = true
        };

        await context.BulkInsertAsync(partsDataList, bulkConfig);
    }

    /// <summary>
    ///     Process model information from the first sheet
    /// </summary>
    private ModelInfo? ProcessModelInfoSheet(ExcelWorksheet worksheet, Guid fileUploadId, string createdBy)
    {
        try
        {
            // Extract model information from the first sheet
            // This is a basic implementation - you may need to adjust based on actual sheet structure
            var modelInfo = new ModelInfo
            {
                FileUploadId = fileUploadId,
                SheetName = worksheet.Name,
                CreatedBy = createdBy
            };

            // Try to extract model information from specific cells or patterns
            // Adjust these based on your actual Excel structure
            for (var row = 1; row <= Math.Min(10, worksheet.Dimension?.End.Row ?? 0); row++)
            for (var col = 1; col <= Math.Min(10, worksheet.Dimension?.End.Column ?? 0); col++)
            {
                var cellValue = worksheet.Cells[row, col].Text?.Trim();
                if (!string.IsNullOrEmpty(cellValue))
                {
                    // Look for model-related information
                    if (cellValue.ToUpper().Contains("MODEL"))
                        modelInfo.ModelCode = worksheet.Cells[row, col + 1].Text?.Trim();
                    else if (cellValue.ToUpper().Contains("NAME"))
                        modelInfo.ModelName = worksheet.Cells[row, col + 1].Text?.Trim();
                    else if (cellValue.ToUpper().Contains("YEAR"))
                        modelInfo.ModelYear = worksheet.Cells[row, col + 1].Text?.Trim();
                }
            }

            return modelInfo;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, $"Error processing model info sheet {worksheet.Name}");
            return null;
        }
    }

    /// <summary>
    ///     Process parts data from a worksheet
    /// </summary>
    private List<PartsData> ProcessPartsDataSheet(ExcelWorksheet worksheet, Guid fileUploadId, string createdBy)
    {
        var partsDataList = new List<PartsData>();

        try
        {
            if (worksheet.Dimension == null) return partsDataList;

            // Find header row (look for known column names)
            var headerRow = FindHeaderRow(worksheet);
            if (headerRow == -1) return partsDataList;

            // Get column mappings
            var columnMappings = GetColumnMappings(worksheet, headerRow);

            // Process data rows
            for (var row = headerRow + 1; row <= worksheet.Dimension.End.Row; row++)
            {
                var partsData = CreatePartsDataFromRow(worksheet, row, columnMappings, fileUploadId, createdBy);
                if (partsData != null)
                {
                    partsData.RowNumber = row;
                    partsDataList.Add(partsData);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error processing parts data sheet {worksheet.Name}");
        }

        return partsDataList;
    }

    /// <summary>
    ///     Find the header row in the worksheet
    /// </summary>
    private int FindHeaderRow(ExcelWorksheet worksheet)
    {
        for (var row = 1; row <= Math.Min(5, worksheet.Dimension?.End.Row ?? 0); row++)
        {
            var firstCellValue = worksheet.Cells[row, 1].Text?.Trim().ToUpper();
            if (firstCellValue == "KD MODEL CODE" || firstCellValue == "KDMODELCODE") return row;
        }

        return 1; // Default to first row if header not found
    }

    /// <summary>
    ///     Get column mappings from header row
    /// </summary>
    private Dictionary<string, int> GetColumnMappings(ExcelWorksheet worksheet, int headerRow)
    {
        var mappings = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

        for (var col = 1; col <= worksheet.Dimension?.End.Column; col++)
        {
            var headerValue = worksheet.Cells[headerRow, col].Text?.Trim();
            if (!string.IsNullOrEmpty(headerValue))
            {
                // Normalize header names - remove spaces, commas, and special characters
                var normalizedHeader = headerValue
                    .Replace(" ", "")
                    .Replace(",", "")
                    .Replace("-", "")
                    .Replace("_", "")
                    .Replace("(", "")
                    .Replace(")", "");

                mappings[normalizedHeader] = col;

                // Also add the original header for exact matches
                mappings[headerValue] = col;
            }
        }

        return mappings;
    }

    /// <summary>
    ///     Create PartsData object from worksheet row
    /// </summary>
    private PartsData? CreatePartsDataFromRow(ExcelWorksheet worksheet, int row,
        Dictionary<string, int> columnMappings, Guid fileUploadId, string createdBy)
    {
        try
        {
            // Check if row has any data
            var firstCellValue = worksheet.Cells[row, 1].Text?.Trim();
            if (string.IsNullOrEmpty(firstCellValue)) return null;

            var partsData = new PartsData
            {
                FileUploadId = fileUploadId,
                SheetName = worksheet.Name,
                CreatedBy = createdBy,
                // Map columns based on header mappings
                KDModelCode = GetCellValue(worksheet, row, columnMappings, "KDModelCode"),
                KDV = GetCellValue(worksheet, row, columnMappings, "KDV"),
                Dealer = GetCellValue(worksheet, row, columnMappings, "Dealer"),
                LVL = GetCellValue(worksheet, row, columnMappings, "LVL"),
                SendType = GetCellValue(worksheet, row, columnMappings, "SendTYPE"),
                KDRefNo = GetCellValue(worksheet, row, columnMappings, "KDREFNO"),
                PartNo = GetCellValue(worksheet, row, columnMappings, "PartNO"),
                InstallLocationComment = GetCellValue(worksheet, row, columnMappings, "InstallLocationComment"),
                QTY = GetCellValue(worksheet, row, columnMappings, "QTY"),
                KDOrderRate = GetCellValue(worksheet, row, columnMappings, "KDOrderRate"),
                SumQTY = GetCellValue(worksheet, row, columnMappings, "SUMQTY"),
                ActualOrderRate = GetCellValue(worksheet, row, columnMappings, "ActualOrderRate"),
                CommentCode = GetCellValue(worksheet, row, columnMappings, "CommentCode"),
                KDColorType = GetCellValue(worksheet, row, columnMappings, "KDColortype"),
                NBRType = GetCellValue(worksheet, row, columnMappings, "NBRType"),
                LocalizationType = GetCellValue(worksheet, row, columnMappings, "LocalizationTYPE"),
                BOMGName = GetCellValue(worksheet, row, columnMappings, "BOMGNAME"),
                BV = GetCellValue(worksheet, row, columnMappings, "BV")
            };

            // Add more mappings as needed...

            return partsData;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, $"Error creating parts data from row {row}");
            return null;
        }
    }

    /// <summary>
    ///     Get cell value by column mapping - tries multiple variations of column names
    /// </summary>
    private string? GetCellValue(ExcelWorksheet worksheet, int row, Dictionary<string, int> columnMappings,
        string columnName)
    {
        // Try exact match first
        if (columnMappings.TryGetValue(columnName, out var columnIndex))
            return worksheet.Cells[row, columnIndex].Text?.Trim();

        // Try normalized version
        var normalizedColumnName = columnName.Replace(" ", "").Replace(",", "").Replace("-", "").Replace("_", "");
        if (columnMappings.TryGetValue(normalizedColumnName, out columnIndex))
            return worksheet.Cells[row, columnIndex].Text?.Trim();

        // Try common variations
        var variations = new[]
        {
            columnName.Replace(" ", ""),
            columnName.Replace(" ", "_"),
            columnName.Replace(" ", "-"),
            columnName.ToUpper(),
            columnName.ToLower()
        };

        foreach (var variation in variations)
            if (columnMappings.TryGetValue(variation, out columnIndex))
                return worksheet.Cells[row, columnIndex].Text?.Trim();

        return null;
    }

    /// <summary>
    ///     Clear all existing KDBOM data using TRUNCATE for better performance
    /// </summary>
    private async Task ClearExistingKDBOMDataAsync()
    {
        await using var context = await contextFactory.CreateDbContextAsync();

        logger.LogInformation("Clearing existing KDBOM data");

        try
        {
            // Use TRUNCATE for much faster clearing (but need to handle foreign keys)
            await context.Database.ExecuteSqlRawAsync("ALTER TABLE kdbom.PartsData NOCHECK CONSTRAINT ALL");
            await context.Database.ExecuteSqlRawAsync("ALTER TABLE kdbom.ModelInfo NOCHECK CONSTRAINT ALL");

            await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE kdbom.PartsData");
            await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE kdbom.ModelInfo");
            await context.Database.ExecuteSqlRawAsync("Delete from kdbom.FileUploads");

            await context.Database.ExecuteSqlRawAsync("ALTER TABLE kdbom.PartsData CHECK CONSTRAINT ALL");
            await context.Database.ExecuteSqlRawAsync("ALTER TABLE kdbom.ModelInfo CHECK CONSTRAINT ALL");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "TRUNCATE failed, falling back to DELETE");
            // Fallback to DELETE if TRUNCATE fails
            await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.PartsData");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.ModelInfo");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM kdbom.FileUploads");
        }

        logger.LogInformation("Existing KDBOM data cleared");
    }

    /// <summary>
    ///     Get all uploaded KDBOM files
    /// </summary>
    public async Task<List<KDBOMFileInfo>> GetUploadedFilesAsync()
    {
        await using var context = await contextFactory.CreateDbContextAsync();

        return await context.KDBOMFileUploads
            .OrderByDescending(f => f.UploadedDate)
            .Select(f => new KDBOMFileInfo
            {
                Id = f.Id,
                FileName = f.FileName,
                OriginalFileName = f.OriginalFileName,
                FileSize = f.FileSize,
                UploadedBy = f.UploadedBy,
                UploadedDate = f.UploadedDate,
                ProcessedDate = f.ProcessedDate,
                ProcessingStatus = f.ProcessingStatus,
                ErrorMessage = f.ErrorMessage,
                RecordCount = f.RecordCount
            })
            .ToListAsync();
    }

    /// <summary>
    ///     Get KDBOM parts data with pagination and filtering
    /// </summary>
    public async Task<(List<KDBOMPartsDataDto> Data, int TotalCount)> GetPartsDataAsync(
        int page = 1, int pageSize = 50, string? searchTerm = null, string? sheetFilter = null)
    {
        await using var context = await contextFactory.CreateDbContextAsync();

        var query = from pd in context.KDBOMPartsData
            join fu in context.KDBOMFileUploads on pd.FileUploadId equals fu.Id
            where fu.ProcessingStatus == "Completed"
            select new KDBOMPartsDataDto
            {
                Id = pd.Id,
                SheetName = pd.SheetName,
                FileName = fu.FileName,
                UploadedBy = fu.UploadedBy,
                UploadedDate = fu.UploadedDate,
                KDModelCode = pd.KDModelCode ?? "",
                PartNo = pd.PartNo ?? "",
                InstallLocationComment = pd.InstallLocationComment ?? "",
                QTY = pd.QTY ?? "",
                BOMGName = pd.BOMGName ?? "",
                Dealer = pd.Dealer ?? "",
                CCCode = pd.CCCode ?? ""
            };

        // Apply filters
        if (!string.IsNullOrEmpty(searchTerm))
            query = query.Where(x =>
                x.KDModelCode.Contains(searchTerm) ||
                x.PartNo.Contains(searchTerm) ||
                x.InstallLocationComment.Contains(searchTerm) ||
                x.BOMGName.Contains(searchTerm));

        if (!string.IsNullOrEmpty(sheetFilter)) query = query.Where(x => x.SheetName == sheetFilter);

        var totalCount = await query.CountAsync();
        var data = await query
            .OrderBy(x => x.FileName)
            .ThenBy(x => x.SheetName)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (data, totalCount);
    }

    /// <summary>
    ///     Get distinct sheet names for filtering
    /// </summary>
    public async Task<List<string>> GetDistinctSheetNamesAsync()
    {
        await using var context = await contextFactory.CreateDbContextAsync();

        return await context.KDBOMPartsData
            .Select(pd => pd.SheetName)
            .Distinct()
            .OrderBy(s => s)
            .ToListAsync();
    }

    /// <summary>
    ///     Clear all KDBOM data and return operation result
    /// </summary>
    public async Task<KDBOMClearResult> ClearKDBOMDataAsync(string clearedBy)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new KDBOMClearResult
        {
            ClearedBy = clearedBy,
            ClearedDate = DateTime.Now
        };

        try
        {
            logger.LogInformation($"Starting KDBOM data clear operation by user: {clearedBy}");

            // Count existing records before clearing for reporting
            await using var context = await contextFactory.CreateDbContextAsync();

            var partsCount = await context.KDBOMPartsData.CountAsync();
            var modelCount = await context.KDBOMModelInfos.CountAsync();
            var fileCount = await context.KDBOMFileUploads.CountAsync();
            var totalRecords = partsCount + modelCount + fileCount;

            logger.LogInformation(
                $"Found {totalRecords} records to clear (Parts: {partsCount}, Models: {modelCount}, Files: {fileCount})");

            // Use existing clear method internally
            await ClearExistingKDBOMDataAsync();

            result.Success = true;
            result.ClearedRecords = totalRecords;
            result.ProcessingTime = stopwatch.Elapsed;

            logger.LogInformation(
                $"Successfully cleared {totalRecords} KDBOM records in {result.ProcessingTime.TotalSeconds:F2} seconds");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error clearing KDBOM data for user: {clearedBy}");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }
}