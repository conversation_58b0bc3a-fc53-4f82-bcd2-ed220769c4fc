﻿@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
            
            <FluentNavGroup Title="Data Management" Icon="@(new Icons.Regular.Size20.CloudAdd())" IconColor="Color.Accent">
                <FluentNavLink Href="ecn/upload" Icon="@(new Icons.Regular.Size20.CloudAdd())" IconColor="Color.Accent">ECN Import</FluentNavLink>
                <FluentNavLink Href="kdbom/import" Icon="@(new Icons.Regular.Size20.DocumentTable())" IconColor="Color.Accent">KDBOM Import</FluentNavLink>
            </FluentNavGroup>
            
            <FluentNavLink Href="ecn/psmc" Icon="@(new Icons.Regular.Size20.Building())" IconColor="Color.Accent">ECN Data</FluentNavLink>
            <FluentNavLink Href="kdbom/data" Icon="@(new Icons.Regular.Size20.Table())" IconColor="Color.Accent">KDBOM Data</FluentNavLink>
            
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
}
