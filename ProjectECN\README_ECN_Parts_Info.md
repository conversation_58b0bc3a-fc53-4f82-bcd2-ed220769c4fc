# ECN Parts Information Feature

## Overview

This feature adds a "Parts Info" action column to the ECN grid that displays a list of parts associated with each ECN. The system checks these parts against KDBOM data and displays matching information including Model Name and Send Type status.

## Features

### 1. Parts Info Action Button
- Added "🔧 Parts Info" button to the Actions column in the ECN grid
- Clicking the button opens a modal dialog showing detailed parts information

### 2. Parts Information Display
The modal shows:
- **Summary Statistics**: Total parts count and number found in KDBOM
- **Parts Table** with the following columns:
  - Part Number (with Old/New part numbers if different)
  - Part Name
  - Page/Line location in ECN
  - BOM Group Name
  - KDBOM Status (Found/Not Found)
  - Model Name from KDBOM
  - Send Type status (Marked/Not Marked)

### 3. KDBOM Integration
- Searches KDBOM data for matching parts using multiple part number fields
- Displays Model Name from KDBOM ModelInfo table
- Shows Send Type status with visual indicators:
  - ✅ Send Type value if marked (not empty)
  - ❌ "Not Marked" if Send Type is empty/null

### 4. Visual Indicators
- **Green background**: Parts found in KDBOM
- **Red background**: Parts not found in KDBOM
- **Status icons**: ✅ for found/marked, ❌ for not found/not marked

## Technical Implementation

### Services
- **ECNPartsInfoService**: Main service for retrieving ECN parts and matching with KDBOM
- **ECNPartsInfoDto**: Data transfer objects for parts information

### Key Methods
- `GetECNPartsInfoAsync(string ecnNumber)`: Retrieves parts info for an ECN
- `FindPartInKDBOMAsync()`: Searches KDBOM for matching parts
- `ViewPartsInfo()`: UI method to display the parts modal

### Database Integration
- Queries `RawEcndata` table for ECN parts information
- Searches `KDBOMPartsData` and `KDBOMModelInfos` tables for matching data
- Matches parts using PartNo, MaterialPartNo, and PBOMPartNumberParent fields

## Usage

1. Navigate to the ECN Processing Results page (`/ecn/psmc`)
2. Find the ECN you want to analyze
3. Click the "🔧 Parts Info" button in the Actions column
4. View the parts information in the modal dialog

## API Testing

A test endpoint is available for testing the functionality:

```
GET /api/test/parts-info/{ecnNumber}
```

Example:
```
GET /api/test/parts-info/52U-0270
```

This returns JSON data with parts information and KDBOM matching details.

## Data Flow

1. **ECN Selection**: User clicks Parts Info button for an ECN
2. **Data Retrieval**: System queries RawEcndata table for all parts in the ECN
3. **KDBOM Matching**: For each part, system searches KDBOM tables for matches
4. **Model Information**: If found, retrieves Model Name from ModelInfo table
5. **Send Type Check**: Determines if Send Type is marked (not empty)
6. **Display**: Shows comprehensive parts information in modal dialog

## Benefits

- **Comprehensive View**: See all parts associated with an ECN in one place
- **KDBOM Integration**: Quickly identify which parts exist in KDBOM
- **Model Information**: View model names and codes for matched parts
- **Send Type Status**: Easily see which parts have Send Type marked
- **Visual Feedback**: Color-coded rows and icons for quick identification

## Future Enhancements

Potential improvements could include:
- Export functionality for parts information
- Filtering and sorting options in the parts table
- Additional KDBOM fields display
- Bulk operations on parts
- Integration with other ECN processing workflows
