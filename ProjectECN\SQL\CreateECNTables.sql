-- ECN Database Tables Creation Script
-- This script creates tables for ECN (Engineering Change Notice) data processing

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ECN')
BEGIN
    CREATE DATABASE ECN
END
GO

USE ECN
GO

-- Create schema for raw ECN data
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'raw')
BEGIN
    EXEC('CREATE SCHEMA raw')
END
GO

-- Create the main ECN data table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'RawECNData' AND schema_id = SCHEMA_ID('raw'))
BEGIN
    CREATE TABLE raw.RawECNData (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        file_name VARCHAR(600) NULL,
        created_date DATETIME NULL,
        ecn_no VARCHAR(20) NULL,
        cc_code VARCHAR(50) NULL,
        prod_type VARCHAR(20) NULL,
        bom_gname VARCHAR(40) NULL,
        page VARCHAR(200) NULL,
        line VARCHAR(10) NULL,
        block VARCHAR(10) NULL,
        old_part_number VARCHAR(50) NULL,
        new_part_number VARCHAR(50) NULL,
        part_name VARCHAR(500) NULL,
        dwg_number VARCHAR(100) NULL,
        dwg_ecn_no VARCHAR(50) NULL,
        belongs_to_psmc BIT DEFAULT 0,
        record_created_date DATETIME DEFAULT GETDATE(),
        record_created_by VARCHAR(50) NULL,
        record_modified_date DATETIME NULL,
        record_modified_by VARCHAR(50) NULL
    )
END
GO

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RawECNData_CreatedDate' AND object_id = OBJECT_ID('raw.RawECNData'))
BEGIN
    CREATE INDEX IX_RawECNData_CreatedDate ON raw.RawECNData(created_date DESC)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RawECNData_ECNNo' AND object_id = OBJECT_ID('raw.RawECNData'))
BEGIN
    CREATE INDEX IX_RawECNData_ECNNo ON raw.RawECNData(ecn_no)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RawECNData_PSMC' AND object_id = OBJECT_ID('raw.RawECNData'))
BEGIN
    CREATE INDEX IX_RawECNData_PSMC ON raw.RawECNData(belongs_to_psmc, created_date)
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RawECNData_PartNumbers' AND object_id = OBJECT_ID('raw.RawECNData'))
BEGIN
    CREATE INDEX IX_RawECNData_PartNumbers ON raw.RawECNData(old_part_number, new_part_number)
END
GO

-- Create a view for easy data retrieval
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ECNDataSummary' AND schema_id = SCHEMA_ID('raw'))
BEGIN
    EXEC('
    CREATE VIEW raw.vw_ECNDataSummary AS
    SELECT 
        created_date,
        COUNT(*) as TotalRecords,
        COUNT(DISTINCT ecn_no) as UniqueECNs,
        SUM(CASE WHEN belongs_to_psmc = 1 THEN 1 ELSE 0 END) as PSMCRecords,
        COUNT(DISTINCT cc_code) as UniqueCCCodes,
        COUNT(DISTINCT prod_type) as UniqueProductTypes
    FROM raw.RawECNData
    WHERE created_date IS NOT NULL
    GROUP BY created_date
    ')
END
GO

-- Create a view for PSMC ECN data
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_PSMCECNData' AND schema_id = SCHEMA_ID('raw'))
BEGIN
    EXEC('
    CREATE VIEW raw.vw_PSMCECNData AS
    SELECT 
        id,
        file_name,
        created_date,
        ecn_no,
        cc_code,
        prod_type,
        bom_gname,
        page,
        line,
        block,
        old_part_number,
        new_part_number,
        part_name,
        dwg_number,
        dwg_ecn_no,
        record_created_date,
        record_created_by
    FROM raw.RawECNData
    WHERE belongs_to_psmc = 1
    ')
END
GO

-- Create stored procedure for data cleanup by date
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_DeleteECNDataByDate' AND schema_id = SCHEMA_ID('raw'))
BEGIN
    EXEC('
    CREATE PROCEDURE raw.sp_DeleteECNDataByDate
        @CreatedDate DATE
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @DeletedCount INT;
        
        DELETE FROM raw.RawECNData 
        WHERE CAST(created_date AS DATE) = @CreatedDate;
        
        SET @DeletedCount = @@ROWCOUNT;
        
        SELECT @DeletedCount as DeletedRecords;
    END
    ')
END
GO

-- Create stored procedure for getting ECN statistics
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetECNStatistics' AND schema_id = SCHEMA_ID('raw'))
BEGIN
    EXEC('
    CREATE PROCEDURE raw.sp_GetECNStatistics
        @StartDate DATE = NULL,
        @EndDate DATE = NULL
    AS
    BEGIN
        SET NOCOUNT ON;
        
        IF @StartDate IS NULL SET @StartDate = DATEADD(MONTH, -1, GETDATE());
        IF @EndDate IS NULL SET @EndDate = GETDATE();
        
        SELECT 
            CAST(created_date AS DATE) as ProcessDate,
            COUNT(*) as TotalRecords,
            COUNT(DISTINCT ecn_no) as UniqueECNs,
            SUM(CASE WHEN belongs_to_psmc = 1 THEN 1 ELSE 0 END) as PSMCRecords,
            COUNT(DISTINCT cc_code) as UniqueCCCodes,
            COUNT(DISTINCT prod_type) as UniqueProductTypes,
            MIN(record_created_date) as FirstProcessed,
            MAX(record_created_date) as LastProcessed
        FROM raw.RawECNData
        WHERE created_date IS NOT NULL
            AND CAST(created_date AS DATE) BETWEEN @StartDate AND @EndDate
        GROUP BY CAST(created_date AS DATE)
        ORDER BY ProcessDate DESC;
    END
    ')
END
GO

PRINT 'ECN tables and objects created successfully!'
PRINT 'Database: ECN'
PRINT 'Schema: raw'
PRINT 'Main Table: raw.RawECNData'
PRINT 'Views: raw.vw_ECNDataSummary, raw.vw_PSMCECNData'
PRINT 'Stored Procedures: raw.sp_DeleteECNDataByDate, raw.sp_GetECNStatistics'
