-- Test script for ECN tables
-- Run this after creating the ECN tables to verify everything works correctly

USE ECN
GO

-- Test 1: Insert sample data
PRINT 'Test 1: Inserting sample ECN data...'

INSERT INTO raw.RawECNData (
    file_name, created_date, ecn_no, cc_code, prod_type, bom_gname,
    page, line, block, old_part_number, new_part_number, part_name,
    dwg_number, dwg_ecn_no, belongs_to_psmc, record_created_by
) VALUES 
('20250127_test.xlsx', '2025-01-27', '52U-0270', 'CC001', '2W', 'BOM_TEST',
 '1', '1', 'A', 'OLD123', 'NEW123', 'Test Part Name',
 'DWG001', '52U-0270', 1, 'test_user'),
('20250127_test.xlsx', '2025-01-27', '52U-0271', 'CC002', '4W', 'BOM_TEST2',
 '1', '2', 'B', 'OLD124', 'NEW124', 'Test Part Name 2',
 'DWG002', '52U-0271', 0, 'test_user'),
('20250126_test.xlsx', '2025-01-26', '52U-0272', 'CC003', '2W', 'BOM_TEST3',
 '1', '1', 'A', 'OLD125', 'NEW125', 'Test Part Name 3',
 'DWG003', '52U-0272', 1, 'test_user')

PRINT 'Sample data inserted successfully!'

-- Test 2: Query the data
PRINT 'Test 2: Querying inserted data...'

SELECT 
    id,
    file_name,
    created_date,
    ecn_no,
    cc_code,
    prod_type,
    belongs_to_psmc,
    record_created_by
FROM raw.RawECNData
ORDER BY created_date DESC, ecn_no

-- Test 3: Test the summary view
PRINT 'Test 3: Testing summary view...'

SELECT * FROM raw.vw_ECNDataSummary
ORDER BY created_date DESC

-- Test 4: Test PSMC view
PRINT 'Test 4: Testing PSMC view...'

SELECT 
    ecn_no,
    created_date,
    cc_code,
    prod_type,
    old_part_number,
    new_part_number
FROM raw.vw_PSMCECNData
ORDER BY created_date DESC

-- Test 5: Test stored procedure for statistics
PRINT 'Test 5: Testing statistics stored procedure...'

EXEC raw.sp_GetECNStatistics @StartDate = '2025-01-25', @EndDate = '2025-01-28'

-- Test 6: Test delete procedure
PRINT 'Test 6: Testing delete procedure...'

-- First, let's see what we have for 2025-01-26
SELECT COUNT(*) as RecordsFor20250126 FROM raw.RawECNData WHERE CAST(created_date AS DATE) = '2025-01-26'

-- Delete records for 2025-01-26
EXEC raw.sp_DeleteECNDataByDate @CreatedDate = '2025-01-26'

-- Verify deletion
SELECT COUNT(*) as RecordsFor20250126AfterDelete FROM raw.RawECNData WHERE CAST(created_date AS DATE) = '2025-01-26'

-- Test 7: Test indexes (check execution plan)
PRINT 'Test 7: Testing index usage...'

-- This should use the ECN number index
SELECT * FROM raw.RawECNData WHERE ecn_no = '52U-0270'

-- This should use the date index
SELECT * FROM raw.RawECNData WHERE created_date >= '2025-01-27'

-- This should use the PSMC index
SELECT * FROM raw.RawECNData WHERE belongs_to_psmc = 1 AND created_date >= '2025-01-27'

-- Test 8: Clean up test data
PRINT 'Test 8: Cleaning up test data...'

DELETE FROM raw.RawECNData WHERE record_created_by = 'test_user'

PRINT 'Test data cleaned up successfully!'

-- Final verification
SELECT COUNT(*) as RemainingTestRecords FROM raw.RawECNData WHERE record_created_by = 'test_user'

PRINT 'ECN tables test completed successfully!'
PRINT 'All tests passed. The ECN database structure is ready for use.'
