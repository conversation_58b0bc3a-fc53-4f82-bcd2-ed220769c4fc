# Design Document

## Overview

This design enhances the KDBOM import functionality by separating data clearing operations from file import operations and restricting uploads to single files. The solution maintains existing service functionality while updating the UI to provide better user control and workflow clarity.

## Architecture

The enhancement follows the existing architecture pattern:
- **Service Layer**: `KDBOMService` - Add new method for clearing data without import
- **UI Layer**: `ImportKDBOM.razor` - Restructure UI to separate operations
- **Data Layer**: Existing Entity Framework context remains unchanged

## Components and Interfaces

### Service Layer Changes

#### KDBOMService Enhancements

```csharp
public class KDBOMService
{
    // New method for standalone data clearing
    public async Task<KDBOMClearResult> ClearKDBOMDataAsync(string clearedBy);
    
    // Modified import methods to NOT clear data automatically
    public async Task<KDBOMUploadResult> ProcessKDBOMFileAsync(
        IBrowserFile file, 
        string uploadedBy, 
        bool clearExistingData = false,
        IProgress<KDBOMUploadProgress>? progress = null);
}
```

#### New Data Transfer Objects

```csharp
public class KDBOMClearResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int ClearedRecords { get; set; }
    public TimeSpan ProcessingTime { get; set; }
    public DateTime ClearedDate { get; set; }
    public string ClearedBy { get; set; }
}
```

### UI Layer Changes

#### ImportKDBOM.razor Structure

```
┌─ Data Management Section
│  ├─ Clear KDBOM Data Button (Warning Style)
│  └─ Clear Status/Results
│
├─ File Import Section  
│  ├─ Single File Selection
│  ├─ Import Button (Primary Style)
│  └─ Import Progress/Results
│
└─ Combined Results Section
   └─ Overall Status Display
```

## Data Models

No changes to existing data models are required. The enhancement uses existing entities:
- `FileUpload`
- `ModelInfo` 
- `PartsData`

## Error Handling

### Clear Operation Errors
- Database connection failures
- Transaction rollback scenarios
- Permission/access errors
- Concurrent operation conflicts

### Import Operation Errors  
- File validation failures
- Single file restriction violations
- Processing errors without auto-clear
- Duplicate data handling

### UI Error Handling
- Confirmation dialog cancellation
- Operation state management
- Button state synchronization
- Progress reporting errors

## Testing Strategy

### Unit Tests
- `KDBOMService.ClearKDBOMDataAsync()` method
- Modified import methods without auto-clear
- Single file validation logic
- Error handling scenarios

### Integration Tests
- Clear operation with database transactions
- Import operation without clearing existing data
- UI state management during operations
- Progress reporting accuracy

### UI Tests
- Single file selection behavior
- Button state management
- Confirmation dialog flow
- Visual feedback accuracy

## Implementation Details

### Service Layer Implementation

#### Clear Data Method
```csharp
public async Task<KDBOMClearResult> ClearKDBOMDataAsync(string clearedBy)
{
    var stopwatch = Stopwatch.StartNew();
    var result = new KDBOMClearResult { ClearedBy = clearedBy };
    
    try
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        // Count records before clearing
        var partsCount = await context.KDBOMPartsData.CountAsync();
        var modelCount = await context.KDBOMModelInfos.CountAsync();
        var fileCount = await context.KDBOMFileUploads.CountAsync();
        
        // Clear data using existing method
        await ClearExistingKDBOMDataAsync();
        
        result.Success = true;
        result.ClearedRecords = partsCount + modelCount + fileCount;
        result.ProcessingTime = stopwatch.Elapsed;
        result.ClearedDate = DateTime.Now;
    }
    catch (Exception ex)
    {
        result.Success = false;
        result.ErrorMessage = ex.Message;
    }
    
    return result;
}
```

#### Modified Import Methods
- Remove automatic calls to `ClearExistingKDBOMDataAsync()`
- Add optional parameter for clearing data
- Update progress reporting to reflect append behavior

### UI Layer Implementation

#### Component State Management
```csharp
private IBrowserFile? _selectedFile;
private bool _isClearingData;
private bool _isImporting;
private KDBOMClearResult? _clearResult;
private KDBOMUploadResult? _importResult;
```

#### Single File Selection
```csharp
private async Task HandleFileSelected(InputFileChangeEventArgs e)
{
    var files = e.GetMultipleFiles(1); // Limit to 1 file
    _selectedFile = files.FirstOrDefault();
    StateHasChanged();
}
```

#### Clear Data Operation
```csharp
private async Task ClearKDBOMData()
{
    var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
        "Are you sure you want to clear all KDBOM data? This action cannot be undone.");
    
    if (!confirmed) return;
    
    _isClearingData = true;
    _clearResult = null;
    StateHasChanged();
    
    try
    {
        _clearResult = await KDBOMService.ClearKDBOMDataAsync("system");
    }
    finally
    {
        _isClearingData = false;
        StateHasChanged();
    }
}
```

### CSS Styling Strategy

#### Section Separation
- Use distinct background colors for Data Management vs File Import
- Add visual borders and spacing between sections
- Use consistent padding and margins

#### Button Styling
- Warning style for Clear button (red/orange theme)
- Primary style for Import button (blue/accent theme)
- Disabled states for both buttons during operations

#### Responsive Design
- Stack sections vertically on mobile
- Maintain button accessibility on all screen sizes
- Preserve visual hierarchy across breakpoints

## Security Considerations

### Data Clearing Security
- Require explicit user confirmation
- Log clearing operations for audit trail
- Prevent concurrent clear/import operations

### File Upload Security
- Maintain existing file size limits (200MB)
- Preserve file type validation (.xlsx, .xls)
- Single file restriction prevents batch upload abuse

## Performance Considerations

### Clear Operation Performance
- Use existing optimized clearing method with TRUNCATE
- Maintain transaction boundaries for data consistency
- Provide progress feedback for large datasets

### Import Performance
- Single file processing reduces memory pressure
- Append-only imports avoid full data reload
- Maintain existing bulk insert optimizations

## Migration Strategy

### Backward Compatibility
- Existing service methods remain functional
- New methods are additive, not breaking changes
- UI changes are purely presentational

### Deployment Approach
1. Deploy service layer changes first
2. Update UI components second
3. Test both operations independently
4. Verify combined workflow functionality