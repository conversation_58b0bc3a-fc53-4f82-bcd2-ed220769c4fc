@page "/ecn/psmc"
@rendermode InteractiveServer
@using ProjectECN.DTO
@using ProjectECN.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Popups
@inject PSMCEcnDataService DataService
@inject ECNPartsInfoService PartsInfoService
@inject IJSRuntime JSRuntime

<div class="psmc-ecn-container">
    <div class="page-header">
        <h2>🏭 ECN Processing Results</h2>
        <p class="page-description">Engineering Change Notice (ECN) processing results</p>
    </div>

    <div class="summary-cards">
        @if (summary != null)
        {
            <div class="summary-card">
                <div class="card-icon">📊</div>
                <div class="card-content">
                    <h3>@summary.PSMCQualified</h3>
                    <p>PSMC Qualified ECNs</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">📋</div>
                <div class="card-content">
                    <h3>@summary.TotalProcessed</h3>
                    <p>Total Processed</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">✅</div>
                <div class="card-content">
                    <h3>@summary.OthersCheckboxMarked</h3>
                    <p>Others Checkbox Marked</p>
                </div>
            </div>
            
            <div class="summary-card">
                <div class="card-icon">🔍</div>
                <div class="card-content">
                    <h3>@summary.PSMCTextDetected</h3>
                    <p>PSMC Text Detected</p>
                </div>
            </div>
        }
    </div>

    <div class="controls-section">
        <div class="search-controls">
            <input type="text" @bind="searchTerm" @onkeyup="HandleSearch" placeholder="Search ECN number..." class="search-input" />
            <button type="button" @onclick="HandleSearch" class="search-button">🔍 Search</button>
        </div>
        
        <div class="filter-controls">
            <select @bind="selectedVehicleType" @bind:after="HandleFilterChange" class="filter-select">
                <option value="">All Vehicle Types</option>
                <option value="2W">2W (Two Wheeler)</option>
                <option value="4W">4W (Four Wheeler)</option>
            </select>

            <input type="date" @bind="startDate" @bind:after="HandleFilterChange" class="date-input" />
            <span>to</span>
            <input type="date" @bind="endDate" @bind:after="HandleFilterChange" class="date-input" />
        </div>
        
        <div class="action-controls">
            <button type="button" @onclick="RefreshData" class="refresh-button">🔄 Refresh</button>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading ECN data...</p>
        </div>
    }
    else
    {
        <SfGrid DataSource="@filteredEcns" AllowPaging="true" AllowSorting="true" AllowFiltering="true">
            <GridPageSettings PageSize="10"></GridPageSettings>
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>
            <GridColumns>
                <GridColumn Field=@nameof(PSMCEcnDto.EcnNumber) HeaderText="ECN Number" AutoFit="true"></GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.VehicleType) HeaderText="Vehicle Type" AutoFit="true"></GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.ProcessDate) HeaderText="Process Date" AutoFit="true" Format="yyyy-MM-dd"></GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.FileName) HeaderText="File Name" AutoFit="true"></GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.OcrConfidence) HeaderText="OCR Confidence" AutoFit="true" Format="N1">
                    <Template>
                        @{
                            var ecn = (context as PSMCEcnDto);
                            @(ecn.OcrConfidence.ToString("F1") + "%")
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.IsOthersCheckboxMarked) HeaderText="Others Checkbox" AutoFit="true">
                    <Template>
                        @{
                            var ecn = (context as PSMCEcnDto);
                            @(ecn.IsOthersCheckboxMarked ? "✅" : "❌")
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.HasPSMCText) HeaderText="PSMC Text" AutoFit="true">
                    <Template>
                        @{
                            var ecn = (context as PSMCEcnDto);
                            @(ecn.HasPSMCText ? "✅" : "❌")
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field=@nameof(PSMCEcnDto.QualifiesForPSMC) HeaderText="PSMC Qualified" AutoFit="true">
                    <Template>
                        @{
                            var ecn = (context as PSMCEcnDto);
                            @(ecn.QualifiesForPSMC ? "✅" : "❌")
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Actions" AutoFit="true">
                    <Template>
                        @{
                            var ecn = (context as PSMCEcnDto);
                            <div class="action-buttons">
                                <SfButton CssClass="e-small" OnClick="@(() => ViewDescriptionImage(ecn))">
                                    <span class="action-icon">📝</span> Description
                                </SfButton>
                                <SfButton CssClass="e-small" OnClick="@(() => ViewFactoryImage(ecn))">
                                    <span class="action-icon">🏭</span> Factory
                                </SfButton>
                                <SfButton CssClass="e-small" OnClick="@(() => ViewOcrText(ecn))">
                                    <span class="action-icon">📄</span> OCR Text
                                </SfButton>
                                <SfButton CssClass="e-small" OnClick="@(() => ViewPartsInfo(ecn))">
                                    <span class="action-icon">🔧</span> Parts Info
                                </SfButton>
                            </div>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    }
</div>

@if (showOcrModal && selectedEcn != null)
{
    <div class="modal-overlay" @onclick="CloseOcrModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>OCR Text - @selectedEcn.EcnNumber</h3>
                <button type="button" @onclick="CloseOcrModal" class="close-button">✕</button>
            </div>
            <div class="modal-body">
                <div class="ocr-text">
                    <pre>@selectedEcn.FactoryOcrText</pre>
                </div>
                <div class="ocr-details">
                    <p><strong>Confidence:</strong> @selectedEcn.OcrConfidence.ToString("F2")%</p>
                    <p><strong>Detected Factories:</strong> @string.Join(", ", selectedEcn.DetectedFactories)</p>
                </div>
            </div>
        </div>
    </div>
}

@if (showDescriptionModal && selectedEcn != null)
{
    <div class="modal-overlay" @onclick="CloseDescriptionModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Description - @selectedEcn.EcnNumber</h3>
                <button type="button" @onclick="CloseDescriptionModal" class="close-button">✕</button>
            </div>
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(selectedEcn.DescriptionImagePath))
                {
                    <div class="image-container">
                        <img src="@ConvertToRelativeUrl(selectedEcn.DescriptionImagePath)" alt="Description Image" />
                    </div>
                }
                else
                {
                    <div class="no-image">
                        <p>No description image available</p>
                    </div>
                }
            </div>
        </div>
    </div>
}

@if (showFactoryModal && selectedEcn != null)
{
    <div class="modal-overlay" @onclick="CloseFactoryModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Factory Image - @selectedEcn.EcnNumber</h3>
                <button type="button" @onclick="CloseFactoryModal" class="close-button">✕</button>
            </div>
            <div class="modal-body">
                @if (!string.IsNullOrEmpty(selectedEcn.FactoryImagePath))
                {
                    <div class="image-container">
                        <img src="@ConvertToRelativeUrl(selectedEcn.FactoryImagePath)" alt="Factory Image" />
                    </div>
                    <div class="checkbox-details">
                        <h4>Detected Checkboxes:</h4>
                        <ul>
                            @foreach (var checkbox in selectedEcn.CheckboxResults)
                            {
                                <li>
                                    <strong>@checkbox.Label:</strong> @(checkbox.IsChecked ? "Checked" : "Unchecked") 
                                    (Confidence: @checkbox.Confidence.ToString("F1")%)
                                </li>
                            }
                        </ul>
                    </div>
                }
                else
                {
                    <div class="no-image">
                        <p>No factory image available</p>
                    </div>
                }
            </div>
        </div>
    </div>
}

<style>
    .psmc-ecn-container {
        padding: 20px;
    }

    .page-header {
        margin-bottom: 20px;
    }

    .page-header h2 {
        margin-bottom: 5px;
    }

    .page-description {
        color: #666;
    }

    .summary-cards {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    .summary-card {
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 15px;
        flex: 1;
        display: flex;
        align-items: center;
    }

    .card-icon {
        font-size: 24px;
        margin-right: 15px;
    }

    .card-content h3 {
        margin: 0;
        font-size: 24px;
    }

    .card-content p {
        margin: 5px 0 0;
        color: #666;
    }

    .controls-section {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
        align-items: center;
    }

    .search-controls, .filter-controls, .action-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .search-input, .filter-select, .date-input {
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        background-color: #0078d4;
        color: white;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px;
    }

    .loading-spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #0078d4;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        background-color: white;
        border-radius: 8px;
        width: 80%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .modal-header h3 {
        margin: 0;
    }

    .close-button {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
    }

    .modal-body {
        padding: 20px;
    }

    .ocr-text {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
        max-height: 300px;
        overflow-y: auto;
    }

    .ocr-text pre {
        margin: 0;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .image-container {
        text-align: center;
        margin-bottom: 15px;
    }

    .image-container img {
        max-width: 100%;
        max-height: 500px;
        border: 1px solid #ddd;
    }

    .no-image {
        text-align: center;
        padding: 40px;
        background-color: #f5f5f5;
        border-radius: 4px;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-icon {
        margin-right: 3px;
    }

    .checkbox-details {
        margin-top: 15px;
    }

    .checkbox-details h4 {
        margin-bottom: 10px;
    }

    .checkbox-details ul {
        list-style-type: none;
        padding: 0;
    }

    .checkbox-details li {
        margin-bottom: 5px;
    }

    /* Parts Info Modal Styles */
    .parts-modal {
        max-width: 1200px;
        width: 95%;
    }

    .parts-summary {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
    }

    .summary-item {
        font-size: 14px;
    }

    .parts-table-container {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .parts-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 13px;
    }

    .parts-table th {
        background-color: #f5f5f5;
        padding: 10px 8px;
        text-align: left;
        border-bottom: 2px solid #ddd;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .parts-table td {
        padding: 8px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    .found-in-kdbom {
        background-color: #f8fff8;
    }

    .not-found-in-kdbom {
        background-color: #fff8f8;
    }

    .part-numbers {
        line-height: 1.3;
    }

    .primary-part {
        font-weight: 600;
        color: #333;
    }

    .old-part, .new-part {
        font-size: 11px;
        color: #666;
        margin-top: 2px;
    }

    .status-found {
        color: #28a745;
        font-weight: 600;
    }

    .status-not-found {
        color: #dc3545;
        font-weight: 600;
    }

    .kdbom-info {
        line-height: 1.3;
    }

    .model-name {
        font-weight: 600;
        color: #333;
    }

    .model-code {
        font-size: 11px;
        color: #666;
        margin-top: 2px;
    }

    .send-type-marked {
        color: #28a745;
        font-weight: 600;
    }

    .send-type-not-marked {
        color: #dc3545;
        font-weight: 600;
    }

    .no-data {
        color: #999;
        font-style: italic;
    }

    .no-parts {
        text-align: center;
        padding: 40px;
        color: #666;
    }
</style>

@code {
    private List<PSMCEcnDto> allEcns = new();
    private List<PSMCEcnDto> filteredEcns = new();
    private PSMCProcessingSummary? summary;
    private bool isLoading = true;
    private string searchTerm = string.Empty;
    private string selectedVehicleType = string.Empty;
    private DateTime? startDate;
    private DateTime? endDate;
    
    // Modal states
    private bool showOcrModal = false;
    private bool showDescriptionModal = false;
    private bool showFactoryModal = false;
    private bool showPartsInfoModal = false;
    private bool isLoadingPartsInfo = false;
    private PSMCEcnDto? selectedEcn;
    private ECNPartsInfoDto? partsInfo;
    
    /// <summary>
    /// Converts an absolute file path to a relative URL for web display
    /// </summary>
    private string ConvertToRelativeUrl(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return string.Empty;
            
        // Check if the path already starts with a web-friendly format
        if (filePath.StartsWith("/"))
            return filePath;
            
        // Find the wwwroot part in the path and convert to relative URL
        const string wwwrootMarker = "wwwroot";
        int wwwrootIndex = filePath.IndexOf(wwwrootMarker, StringComparison.OrdinalIgnoreCase);
        
        if (wwwrootIndex >= 0)
        {
            // Get the part after "wwwroot" and replace backslashes with forward slashes
            string relativePath = filePath.Substring(wwwrootIndex + wwwrootMarker.Length)
                .Replace('\\', '/');
                
            // Ensure the path starts with a forward slash
            if (!relativePath.StartsWith("/"))
                relativePath = "/" + relativePath;
                
            return relativePath;
        }
        
        // If we can't find wwwroot, try to extract from a path like E:\OfficeProjects\ProjectECN\ProjectECN\wwwroot\enc\...
        if (filePath.Contains("\\enc\\") || filePath.Contains("/enc/"))
        {
            int encIndex = filePath.IndexOf("\\enc\\", StringComparison.OrdinalIgnoreCase);
            if (encIndex < 0)
                encIndex = filePath.IndexOf("/enc/", StringComparison.OrdinalIgnoreCase);
                
            if (encIndex >= 0)
            {
                string relativePath = filePath.Substring(encIndex).Replace('\\', '/');
                return relativePath;
            }
        }
        
        // If all else fails, just return the original path
        return filePath;
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load all ECNs instead of just qualified ones
            allEcns = await DataService.LoadPSMCEcnDataAsync();
            summary = await DataService.GetProcessingSummaryAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading ECN data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void HandleSearch()
    {
        ApplyFilters();
    }

    private void HandleFilterChange()
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        var filtered = allEcns.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filtered = filtered.Where(e =>
                e.EcnNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                e.FileName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply vehicle type filter
        if (!string.IsNullOrWhiteSpace(selectedVehicleType))
        {
            filtered = filtered.Where(e =>
                e.VehicleType.Equals(selectedVehicleType, StringComparison.OrdinalIgnoreCase));
        }

        // Apply date range filter
        if (startDate.HasValue)
        {
            filtered = filtered.Where(e => e.ProcessDateTime >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            filtered = filtered.Where(e => e.ProcessDateTime <= endDate.Value);
        }

        filteredEcns = filtered.OrderByDescending(e => e.ProcessedAt).ToList();
        StateHasChanged();
    }

    private void ViewOcrText(PSMCEcnDto ecn)
    {
        selectedEcn = ecn;
        showOcrModal = true;
        StateHasChanged();
    }

    private void CloseOcrModal()
    {
        showOcrModal = false;
        selectedEcn = null;
        StateHasChanged();
    }

    private void ViewDescriptionImage(PSMCEcnDto ecn)
    {
        selectedEcn = ecn;
        showDescriptionModal = true;
        StateHasChanged();
    }

    private void CloseDescriptionModal()
    {
        showDescriptionModal = false;
        selectedEcn = null;
        StateHasChanged();
    }

    private void ViewFactoryImage(PSMCEcnDto ecn)
    {
        selectedEcn = ecn;
        showFactoryModal = true;
        StateHasChanged();
    }

    private void CloseFactoryModal()
    {
        showFactoryModal = false;
        selectedEcn = null;
        StateHasChanged();
    }

    private async Task ViewPartsInfo(PSMCEcnDto ecn)
    {
        selectedEcn = ecn;
        showPartsInfoModal = true;
        isLoadingPartsInfo = true;
        partsInfo = null;
        StateHasChanged();

        try
        {
            partsInfo = await PartsInfoService.GetECNPartsInfoAsync(ecn.EcnNumber);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading parts info for ECN {ecn.EcnNumber}: {ex.Message}");
            // You might want to show an error message to the user
        }
        finally
        {
            isLoadingPartsInfo = false;
            StateHasChanged();
        }
    }

    private void ClosePartsInfoModal()
    {
        showPartsInfoModal = false;
        selectedEcn = null;
        partsInfo = null;
        isLoadingPartsInfo = false;
        StateHasChanged();
    }
}

@if (showPartsInfoModal && selectedEcn != null && partsInfo != null)
{
    <div class="modal-overlay" @onclick="ClosePartsInfoModal">
        <div class="modal-content parts-modal" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Parts Information - @selectedEcn.EcnNumber</h3>
                <button type="button" @onclick="ClosePartsInfoModal" class="close-button">✕</button>
            </div>
            <div class="modal-body">
                @if (isLoadingPartsInfo)
                {
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading parts information...</p>
                    </div>
                }
                else
                {
                    <div class="parts-summary">
                        <div class="summary-item">
                            <strong>Total Parts:</strong> @partsInfo.TotalParts
                        </div>
                        <div class="summary-item">
                            <strong>Found in KDBOM:</strong> @partsInfo.PartsFoundInKDBOM
                        </div>
                        @if (partsInfo.CreatedDate.HasValue)
                        {
                            <div class="summary-item">
                                <strong>ECN Date:</strong> @partsInfo.CreatedDate.Value.ToString("yyyy-MM-dd")
                            </div>
                        }
                    </div>

                    @if (partsInfo.Parts.Any())
                    {
                        <div class="parts-table-container">
                            <table class="parts-table">
                                <thead>
                                    <tr>
                                        <th>Part Number</th>
                                        <th>Part Name</th>
                                        <th>Page/Line</th>
                                        <th>BOM Group</th>
                                        <th>KDBOM Status</th>
                                        <th>Model Name</th>
                                        <th>Send Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var part in partsInfo.Parts)
                                    {
                                        <tr class="@(part.FoundInKDBOM ? "found-in-kdbom" : "not-found-in-kdbom")">
                                            <td>
                                                <div class="part-numbers">
                                                    <div class="primary-part">@part.PartNumber</div>
                                                    @if (!string.IsNullOrEmpty(part.OldPartNumber) && part.OldPartNumber != part.PartNumber)
                                                    {
                                                        <div class="old-part">Old: @part.OldPartNumber</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(part.NewPartNumber) && part.NewPartNumber != part.PartNumber)
                                                    {
                                                        <div class="new-part">New: @part.NewPartNumber</div>
                                                    }
                                                </div>
                                            </td>
                                            <td>@part.PartName</td>
                                            <td>@part.Page/@part.Line</td>
                                            <td>@part.BomGname</td>
                                            <td>
                                                @if (part.FoundInKDBOM)
                                                {
                                                    <span class="status-found">✅ Found</span>
                                                }
                                                else
                                                {
                                                    <span class="status-not-found">❌ Not Found</span>
                                                }
                                            </td>
                                            <td>
                                                @if (part.KDBOMMatch != null)
                                                {
                                                    <div class="kdbom-info">
                                                        <div class="model-name">@part.KDBOMMatch.ModelName</div>
                                                        @if (!string.IsNullOrEmpty(part.KDBOMMatch.ModelCode))
                                                        {
                                                            <div class="model-code">(@part.KDBOMMatch.ModelCode)</div>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="no-data">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (part.KDBOMMatch != null)
                                                {
                                                    @if (part.KDBOMMatch.IsSendTypeMarked)
                                                    {
                                                        <span class="send-type-marked">✅ @part.KDBOMMatch.SendType</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="send-type-not-marked">❌ Not Marked</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="no-data">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="no-parts">
                            <p>No parts found for this ECN.</p>
                        </div>
                    }
                }
            </div>
        </div>
    </div>
}